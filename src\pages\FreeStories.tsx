
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Play, 
  Pause, 
  Heart, 
  Clock, 
  Star, 
  Search,
  Filter,
  Volume2
} from "lucide-react";
import { freeStories } from "@/data/dummyData";
import { toast } from "@/hooks/use-toast";

const FreeStories = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [playingStory, setPlayingStory] = useState<string | null>(null);
  const [likedStories, setLikedStories] = useState<Set<string>>(new Set());

  const filteredStories = freeStories.filter(story => {
    const matchesSearch = story.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         story.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         story.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || 
                           story.tags.some(tag => tag.toLowerCase() === selectedCategory.toLowerCase());
    
    return matchesSearch && matchesCategory;
  });

  const allTags = Array.from(new Set(freeStories.flatMap(story => story.tags)));

  const playStory = (storyId: string, storyTitle: string) => {
    if (playingStory === storyId) {
      setPlayingStory(null);
      toast({
        title: "Playback stopped",
        description: "Story narration paused",
      });
    } else {
      setPlayingStory(storyId);
      toast({
        title: `Now playing: ${storyTitle}`,
        description: "🎙️ Audio playback simulation started",
      });
    }
  };

  const toggleLike = (storyId: string) => {
    const newLikedStories = new Set(likedStories);
    if (likedStories.has(storyId)) {
      newLikedStories.delete(storyId);
      toast({
        title: "Removed from favorites",
        description: "Story removed from your favorites",
      });
    } else {
      newLikedStories.add(storyId);
      toast({
        title: "Added to favorites! ❤️",
        description: "Story saved to your favorites",
      });
    }
    setLikedStories(newLikedStories);
  };

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-red-500 to-orange-500 bg-clip-text text-transparent">
          Free Stories Library 📚
        </h1>
        <p className="text-xl text-muted-foreground">
          Discover magical tales narrated by our amazing voice actors
        </p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search stories, tags, or keywords..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[200px]">
            <Filter className="w-4 h-4 mr-2" />
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {allTags.map((tag) => (
              <SelectItem key={tag} value={tag.toLowerCase()}>
                {tag}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Stories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredStories.map((story) => (
          <Card key={story.id} className="h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer group">
            <div className="aspect-video bg-gradient-to-r from-purple-400 to-pink-400 rounded-t-lg relative overflow-hidden">
              {story.thumbnail && (
                <img 
                  src={story.thumbnail} 
                  alt={story.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              )}
              <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                <Button
                  size="lg"
                  variant="secondary"
                  className="opacity-0 group-hover:opacity-100 transition-opacity bg-white/90 hover:bg-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    playStory(story.id, story.title);
                  }}
                >
                  {playingStory === story.id ? (
                    <>
                      <Pause className="w-6 h-6 mr-2" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="w-6 h-6 mr-2" />
                      Play
                    </>
                  )}
                </Button>
              </div>
              {playingStory === story.id && (
                <div className="absolute top-4 left-4">
                  <Badge className="bg-green-500 text-white animate-pulse">
                    <Volume2 className="w-3 h-3 mr-1" />
                    Playing
                  </Badge>
                </div>
              )}
            </div>
            
            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg leading-tight">{story.title}</CardTitle>
                <div className="flex items-center gap-2 flex-shrink-0 ml-2">
                  <Badge variant="secondary" className="text-xs">
                    <Clock className="w-3 h-3 mr-1" />
                    {story.duration}m
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleLike(story.id);
                    }}
                    className="p-1 h-auto"
                  >
                    <Heart 
                      className={`w-4 h-4 ${
                        likedStories.has(story.id) 
                          ? 'text-red-500 fill-current' 
                          : 'text-muted-foreground hover:text-red-500'
                      }`} 
                    />
                  </Button>
                </div>
              </div>
              <CardDescription className="line-clamp-3">
                {story.content.substring(0, 120)}...
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      🎙️ {story.narrator}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{story.likes}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {story.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                
                <Button 
                  className="w-full story-gradient text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    playStory(story.id, story.title);
                  }}
                >
                  {playingStory === story.id ? (
                    <>
                      <Pause className="w-4 h-4 mr-2" />
                      Pause Story
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Listen Now
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredStories.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">📚</div>
          <h3 className="text-xl font-semibold mb-2">No stories found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search terms or category filter
          </p>
        </div>
      )}
    </div>
  );
};

export default FreeStories;
