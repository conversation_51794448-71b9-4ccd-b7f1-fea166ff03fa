import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  Rewind, 
  FastForward 
} from 'lucide-react';

interface AudioPlayerProps {
  audioUrl: string;
  title?: string;
  narrator?: string;
  className?: string;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioUrl,
  title,
  narrator,
  className = ''
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  const handlePlay = async () => {
    if (audioRef.current) {
      try {
        await audioRef.current.play();
        setIsPlaying(true);
      } catch (error) {
        console.error('Error playing audio:', error);
      }
    }
  };

  const handlePause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      setCurrentTime(0);
    }
  };

  const handleSeek = (value: number[]) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value[0];
      setCurrentTime(value[0]);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
    setIsMuted(false);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const skipBackward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 10);
    }
  };

  const skipForward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.min(duration, audioRef.current.currentTime + 10);
    }
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!audioUrl) {
    return (
      <div className={`w-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl p-8 border-4 border-gray-300 ${className}`}>
        <div className="text-center">
          <div className="text-6xl mb-4">🔇</div>
          <div className="text-xl font-bold text-gray-600">No sound yet!</div>
          <div className="text-lg font-semibold text-gray-500">Generate audio to hear your story!</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full bg-gradient-to-br from-purple-100 to-pink-100 rounded-3xl p-8 border-4 border-purple-300 shadow-xl ${className}`}>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      {(title || narrator) && (
        <div className="text-center space-y-3 mb-6">
          <div className="text-5xl animate-bounce">🎵</div>
          {title && <h3 className="text-2xl font-black text-purple-700">{title}</h3>}
          {narrator && (
            <p className="text-lg font-bold text-purple-600 bg-white px-4 py-2 rounded-2xl border-2 border-purple-300 inline-block">
              🎙️ Read by {narrator}
            </p>
          )}
        </div>
      )}

      <div className="space-y-6">
        <div className="bg-white rounded-2xl p-6 border-4 border-pink-200 shadow-lg space-y-4">
          <div className="text-center mb-4">
            <div className="text-2xl font-black text-pink-700 mb-2">Story Player 🎧</div>
            <p className="text-lg font-bold text-pink-600">Listen to your amazing story!</p>
          </div>
          
          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={1}
            onValueChange={handleSeek}
            className="w-full h-3"
            disabled={!audioUrl || isLoading}
          />
          <div className="flex justify-between text-lg font-bold text-purple-600">
            <span className="bg-purple-100 px-3 py-1 rounded-full">{formatTime(currentTime)}</span>
            <span className="bg-purple-100 px-3 py-1 rounded-full">{formatTime(duration)}</span>
          </div>
        </div>

        <div className="flex items-center justify-center space-x-4">
          <Button
            onClick={skipBackward}
            disabled={!audioUrl || isLoading}
            className="bg-gradient-to-r from-blue-400 to-purple-400 hover:from-blue-500 hover:to-purple-500 text-white font-bold w-16 h-16 rounded-full shadow-lg transform transition-all hover:scale-110 disabled:opacity-50"
          >
            <Rewind className="w-6 h-6" />
          </Button>

          <Button
            onClick={isPlaying ? handlePause : handlePlay}
            disabled={!audioUrl || isLoading}
            className="bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 text-white font-bold w-20 h-20 rounded-full shadow-xl transform transition-all hover:scale-110 disabled:opacity-50"
          >
            {isLoading ? (
              <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" />
            ) : isPlaying ? (
              <Pause className="w-8 h-8" />
            ) : (
              <Play className="w-8 h-8" />
            )}
          </Button>

          <Button
            onClick={handleStop}
            disabled={!audioUrl || isLoading}
            className="bg-gradient-to-r from-red-400 to-pink-400 hover:from-red-500 hover:to-pink-500 text-white font-bold w-16 h-16 rounded-full shadow-lg transform transition-all hover:scale-110 disabled:opacity-50"
          >
            <Square className="w-6 h-6" />
          </Button>

          <Button
            onClick={skipForward}
            disabled={!audioUrl || isLoading}
            className="bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500 text-white font-bold w-16 h-16 rounded-full shadow-lg transform transition-all hover:scale-110 disabled:opacity-50"
          >
            <FastForward className="w-6 h-6" />
          </Button>
        </div>

        <div className="bg-white rounded-2xl p-4 border-4 border-yellow-200 shadow-lg">
          <div className="text-center mb-3">
            <div className="text-lg font-black text-yellow-700">🔊 Volume Control</div>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              onClick={toggleMute}
              className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white font-bold w-12 h-12 rounded-full shadow-lg transform transition-all hover:scale-110"
            >
              {isMuted ? (
                <VolumeX className="w-6 h-6" />
              ) : (
                <Volume2 className="w-6 h-6" />
              )}
            </Button>
            <div className="flex-1">
              <Slider
                value={[isMuted ? 0 : volume]}
                max={1}
                step={0.1}
                onValueChange={handleVolumeChange}
                className="h-3"
              />
            </div>
            <div className="text-lg font-bold text-yellow-700 bg-yellow-100 px-3 py-1 rounded-full min-w-[60px] text-center">
              {Math.round((isMuted ? 0 : volume) * 100)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
