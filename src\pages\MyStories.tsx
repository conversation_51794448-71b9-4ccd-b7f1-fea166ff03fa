
import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";
import { 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  Download, 
  Share,
  Clock,
  Star,
  Volume2,
  Plus,
  BookOpen
} from "lucide-react";
import { userStories } from "@/data/dummyData";
import { toast } from "@/hooks/use-toast";

const MyStories = () => {
  const { user } = useAuth();
  const [playingStory, setPlayingStory] = useState<string | null>(null);

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="text-6xl mb-4">🔒</div>
        <h2 className="text-2xl font-bold mb-4"><PERSON><PERSON> Required</h2>
        <p className="text-muted-foreground mb-6">
          Please log in to view and manage your personal story collection.
        </p>
        <Link to="/">
          <Button className="story-gradient text-white">
            Back to Home
          </Button>
        </Link>
      </div>
    );
  }

  const playStory = (storyId: string, storyTitle: string) => {
    if (playingStory === storyId) {
      setPlayingStory(null);
      toast({
        title: "Playback stopped",
        description: "Story narration paused",
      });
    } else {
      setPlayingStory(storyId);
      toast({
        title: `Now playing: ${storyTitle}`,
        description: "🎙️ Audio playback simulation started",
      });
    }
  };

  const deleteStory = (storyId: string, storyTitle: string) => {
    toast({
      title: "Story deleted",
      description: `"${storyTitle}" has been removed from your collection`,
    });
  };

  const shareStory = (storyTitle: string) => {
    toast({
      title: "Story shared! 🚀",
      description: `"${storyTitle}" has been shared to the community`,
    });
  };

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent">
            My Stories 📖
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            Your personal collection of magical tales
          </p>
        </div>
        <Link to="/generate">
          <Button className="story-gradient text-white">
            <Plus className="w-4 h-4 mr-2" />
            Create New Story
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{userStories.length}</div>
            <div className="text-sm text-muted-foreground">Total Stories</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {userStories.reduce((acc, story) => acc + story.duration, 0)}m
            </div>
            <div className="text-sm text-muted-foreground">Total Duration</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">
              {userStories.reduce((acc, story) => acc + story.likes, 0)}
            </div>
            <div className="text-sm text-muted-foreground">Total Likes</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">🏆</div>
            <div className="text-sm text-muted-foreground">Top Creator</div>
          </CardContent>
        </Card>
      </div>

      {/* Stories List */}
      {userStories.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="text-6xl mb-4">✨</div>
            <h3 className="text-xl font-semibold mb-2">No stories yet</h3>
            <p className="text-muted-foreground mb-6">
              Start creating magical stories with our AI generator!
            </p>
            <Link to="/generate">
              <Button className="story-gradient text-white">
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Story
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userStories.map((story) => (
            <Card key={story.id} className="h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <div className="aspect-video bg-gradient-to-r from-indigo-400 to-purple-400 rounded-t-lg relative overflow-hidden">
                {story.thumbnail && (
                  <img 
                    src={story.thumbnail} 
                    alt={story.title}
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                  <Button
                    size="lg"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white"
                    onClick={() => playStory(story.id, story.title)}
                  >
                    {playingStory === story.id ? (
                      <>
                        <Pause className="w-6 h-6 mr-2" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="w-6 h-6 mr-2" />
                        Play
                      </>
                    )}
                  </Button>
                </div>
                {playingStory === story.id && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-green-500 text-white animate-pulse">
                      <Volume2 className="w-3 h-3 mr-1" />
                      Playing
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardHeader>
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg leading-tight">{story.title}</CardTitle>
                  <Badge variant="secondary" className="ml-2 flex-shrink-0">
                    <Clock className="w-3 h-3 mr-1" />
                    {story.duration}m
                  </Badge>
                </div>
                <CardDescription className="line-clamp-3">
                  {story.content.substring(0, 120)}...
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        🎙️ {story.narrator}
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">{story.likes}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1">
                    {story.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      className="flex-1 story-gradient text-white"
                      onClick={() => playStory(story.id, story.title)}
                    >
                      {playingStory === story.id ? (
                        <Pause className="w-4 h-4 mr-1" />
                      ) : (
                        <Play className="w-4 h-4 mr-1" />
                      )}
                      {playingStory === story.id ? 'Pause' : 'Play'}
                    </Button>
                    
                    <Button variant="outline" size="sm">
                      <Edit className="w-4 h-4" />
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => shareStory(story.title)}
                    >
                      <Share className="w-4 h-4" />
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteStory(story.id, story.title)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default MyStories;
