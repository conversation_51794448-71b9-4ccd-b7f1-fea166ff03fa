
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";
import { 
  Video, 
  Play, 
  Pause, 
  Download, 
  Share,
  Loader2,
  Wand2,
  Settings,
  Eye,
  Upload
} from "lucide-react";
import { userStories } from "@/data/dummyData";
import { toast } from "@/hooks/use-toast";

const VideoStudio = () => {
  const { user } = useAuth();
  const [selectedStory, setSelectedStory] = useState("");
  const [videoStyle, setVideoStyle] = useState("animated");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedVideo, setGeneratedVideo] = useState<any>(null);

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="text-6xl mb-4">🔒</div>
        <h2 className="text-2xl font-bold mb-4">Login Required</h2>
        <p className="text-muted-foreground mb-6">
          Please log in to access the Video Studio and create amazing story videos.
        </p>
        <Link to="/">
          <Button className="story-gradient text-white">
            Back to Home
          </Button>
        </Link>
      </div>
    );
  }

  const videoStyles = [
    {
      id: "animated",
      name: "Animated Scenes",
      description: "Colorful animated backgrounds with text overlays",
      preview: "🎨"
    },
    {
      id: "cinematic",
      name: "Cinematic",
      description: "Movie-like scenes with dramatic lighting",
      preview: "🎬"
    },
    {
      id: "storybook",
      name: "Storybook Style",
      description: "Classic children's book illustrations",
      preview: "📖"
    },
    {
      id: "nature",
      name: "Nature Scenes",
      description: "Beautiful natural landscapes and environments",
      preview: "🌲"
    }
  ];

  const generateVideo = async () => {
    if (!selectedStory) {
      toast({
        title: "Please select a story",
        description: "Choose a story from your collection to convert to video",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    // Simulate video generation progress
    const progressInterval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 500);

    // Simulate generation time
    await new Promise(resolve => setTimeout(resolve, 6000));
    
    clearInterval(progressInterval);
    setGenerationProgress(100);
    
    const story = userStories.find(s => s.id === selectedStory);
    setGeneratedVideo({
      id: Date.now().toString(),
      storyId: selectedStory,
      title: story?.title + " - Video",
      duration: story?.duration || 0,
      style: videoStyle,
      thumbnail: story?.thumbnail || "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400"
    });
    
    setIsGenerating(false);
    
    toast({
      title: "Video generated! 🎬",
      description: "Your story has been transformed into a beautiful video!",
    });
  };

  const downloadVideo = () => {
    toast({
      title: "Download started! 📥",
      description: "Your video is being prepared for download...",
    });
  };

  const shareVideo = () => {
    toast({
      title: "Video shared! 🚀",
      description: "Your video has been shared to the community!",
    });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent">
          Video Studio 🎬
        </h1>
        <p className="text-xl text-muted-foreground">
          Transform your stories into stunning animated videos
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Video Creation Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Video className="w-5 h-5" />
              Create Video
            </CardTitle>
            <CardDescription>
              Select a story and style to generate your video
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select Story</label>
              <Select value={selectedStory} onValueChange={setSelectedStory}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a story from your collection" />
                </SelectTrigger>
                <SelectContent>
                  {userStories.map((story) => (
                    <SelectItem key={story.id} value={story.id}>
                      <div className="flex items-center gap-2">
                        <span>{story.title}</span>
                        <Badge variant="outline" className="text-xs">
                          {story.duration}m
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {userStories.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  <Link to="/generate" className="text-primary hover:underline">
                    Create some stories first
                  </Link> to generate videos.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Video Style</label>
              <div className="grid grid-cols-2 gap-3">
                {videoStyles.map((style) => (
                  <div
                    key={style.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      videoStyle === style.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setVideoStyle(style.id)}
                  >
                    <div className="text-2xl mb-1">{style.preview}</div>
                    <div className="text-sm font-medium">{style.name}</div>
                    <div className="text-xs text-muted-foreground">{style.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {isGenerating && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Generating Video...</span>
                  <span className="text-sm text-muted-foreground">{Math.round(generationProgress)}%</span>
                </div>
                <Progress value={generationProgress} className="w-full" />
                <p className="text-xs text-muted-foreground">
                  This may take a few minutes. We're creating something magical! ✨
                </p>
              </div>
            )}

            <Button 
              onClick={generateVideo} 
              disabled={isGenerating || !selectedStory}
              className="w-full story-gradient text-white"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Video...
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                  Generate Video
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Preview Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Video Preview
            </CardTitle>
            <CardDescription>
              Preview your generated video
            </CardDescription>
          </CardHeader>
          <CardContent>
            {generatedVideo ? (
              <div className="space-y-4">
                <div className="aspect-video bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg relative overflow-hidden">
                  <img 
                    src={generatedVideo.thumbnail} 
                    alt={generatedVideo.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                    <Button size="lg" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Play className="w-6 h-6 mr-2" />
                      Play Video
                    </Button>
                  </div>
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-green-500 text-white">
                      ✨ Generated
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h3 className="font-semibold">{generatedVideo.title}</h3>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      {generatedVideo.duration} minutes
                    </Badge>
                    <Badge variant="outline">
                      {videoStyles.find(s => s.id === generatedVideo.style)?.name}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button className="flex-1" onClick={downloadVideo}>
                    <Download className="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  <Button variant="outline" onClick={shareVideo}>
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            ) : (
              <div className="aspect-video border-2 border-dashed border-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <Video className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">
                    Your generated video will appear here
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Videos */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Recent Videos</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {generatedVideo && (
            <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <div className="aspect-video bg-gradient-to-r from-blue-400 to-purple-400 rounded-t-lg relative overflow-hidden">
                <img 
                  src={generatedVideo.thumbnail} 
                  alt={generatedVideo.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                  <Button size="lg" variant="secondary" className="bg-white/90 hover:bg-white">
                    <Play className="w-6 h-6 mr-2" />
                    Play
                  </Button>
                </div>
              </div>
              <CardHeader>
                <CardTitle className="text-lg">{generatedVideo.title}</CardTitle>
                <CardDescription>
                  Created from "{userStories.find(s => s.id === generatedVideo.storyId)?.title}"
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2">
                  <Button size="sm" className="flex-1">
                    <Play className="w-4 h-4 mr-1" />
                    Play
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        
        {!generatedVideo && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-6xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold mb-2">No videos yet</h3>
              <p className="text-muted-foreground">
                Generate your first video from one of your stories!
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default VideoStudio;
