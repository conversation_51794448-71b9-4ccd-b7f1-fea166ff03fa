﻿export interface TTSOptions {
  text: string;
  voice: string;
  speed?: number;
  pitch?: number;
}

export interface TTSResponse {
  audioUrl: string;
  duration: number;
  success: boolean;
  error?: string;
}

export interface VoiceOption {
  id: string;
  name: string;
  gender: string;
  age: string;
  accent: string;
  preview_url?: string;
}

// Web Speech API TTS Service (Fallback)
class WebSpeechTTSService {
  private synthesis: SpeechSynthesis;
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.synthesis = window.speechSynthesis;
    this.loadVoices();
  }

  private loadVoices() {
    this.voices = this.synthesis.getVoices();
    if (this.voices.length === 0) {
      this.synthesis.onvoiceschanged = () => {
        this.voices = this.synthesis.getVoices();
      };
    }
  }

  async generateSpeech(options: TTSOptions): Promise<TTSResponse> {
    return new Promise((resolve) => {
      try {
        const utterance = new SpeechSynthesisUtterance(options.text);
        
        const voice = this.voices.find(v => v.name === options.voice) || this.voices[0];
        if (voice) {
          utterance.voice = voice;
        }

        utterance.rate = options.speed || 1.0;
        utterance.pitch = options.pitch || 1.0;

        const wordCount = options.text.split(/\s+/).length;
        const estimatedDuration = (wordCount / 150) * 60;

        utterance.onend = () => {
          resolve({
            audioUrl: '',
            duration: estimatedDuration,
            success: true,
          });
        };

        utterance.onerror = (error) => {
          resolve({
            audioUrl: '',
            duration: 0,
            success: false,
            error: error.error,
          });
        };

        this.synthesis.speak(utterance);
      } catch (error) {
        resolve({
          audioUrl: '',
          duration: 0,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    });
  }

  async getVoices(): Promise<VoiceOption[]> {
    if (this.voices.length === 0) {
      this.loadVoices();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return this.voices.map(voice => ({
      id: voice.name,
      name: voice.name,
      gender: voice.name.toLowerCase().includes('female') ? 'female' : 
             voice.name.toLowerCase().includes('male') ? 'male' : 'unknown',
      age: 'unknown',
      accent: voice.lang || 'unknown',
    }));
  }

  stop() {
    this.synthesis.cancel();
  }
}

// Main TTS Service
class TTSService {
  private webSpeechService: WebSpeechTTSService;
  private currentAudio?: HTMLAudioElement;

  constructor() {
    this.webSpeechService = new WebSpeechTTSService();
  }

  async generateSpeech(options: TTSOptions): Promise<TTSResponse> {
    return this.webSpeechService.generateSpeech(options);
  }

  async getAvailableVoices(): Promise<VoiceOption[]> {
    return this.webSpeechService.getVoices();
  }

  playAudio(audioUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.currentAudio) {
        this.currentAudio.pause();
      }

      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.onended = () => resolve();
      this.currentAudio.onerror = () => reject(new Error('Audio playback failed'));
      this.currentAudio.play().catch(reject);
    });
  }

  stopAudio() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
    }
    this.webSpeechService.stop();
  }

  pauseAudio() {
    if (this.currentAudio) {
      this.currentAudio.pause();
    }
  }

  resumeAudio() {
    if (this.currentAudio) {
      this.currentAudio.play();
    }
  }

  getCurrentTime(): number {
    return this.currentAudio?.currentTime || 0;
  }

  getDuration(): number {
    return this.currentAudio?.duration || 0;
  }

  setCurrentTime(time: number) {
    if (this.currentAudio) {
      this.currentAudio.currentTime = time;
    }
  }
}

export const ttsService = new TTSService();
export default ttsService;
