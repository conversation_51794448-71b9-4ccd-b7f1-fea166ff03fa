
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom storytelling theme colors */
@layer base {
  :root {
    --background: 250 100% 98%;
    --foreground: 220 65% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 65% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 65% 18%;

    --primary: 240 73% 65%;
    --primary-foreground: 0 0% 98%;

    --secondary: 250 60% 95%;
    --secondary-foreground: 220 65% 18%;

    --muted: 250 60% 95%;
    --muted-foreground: 220 16% 46%;

    --accent: 280 65% 90%;
    --accent-foreground: 220 65% 18%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 250 30% 88%;
    --input: 250 30% 88%;
    --ring: 240 73% 65%;

    --radius: 0.75rem;

    --sidebar-background: 250 100% 98%;
    --sidebar-foreground: 220 65% 18%;
    --sidebar-primary: 240 73% 65%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 250 60% 95%;
    --sidebar-accent-foreground: 220 65% 18%;
    --sidebar-border: 250 30% 88%;
    --sidebar-ring: 240 73% 65%;
  }

  .dark {
    --background: 220 65% 4%;
    --foreground: 250 100% 98%;

    --card: 220 65% 6%;
    --card-foreground: 250 100% 98%;

    --popover: 220 65% 6%;
    --popover-foreground: 250 100% 98%;

    --primary: 240 73% 65%;
    --primary-foreground: 220 65% 4%;

    --secondary: 220 50% 10%;
    --secondary-foreground: 250 100% 98%;

    --muted: 220 50% 10%;
    --muted-foreground: 250 20% 65%;

    --accent: 280 65% 15%;
    --accent-foreground: 250 100% 98%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 50% 15%;
    --input: 220 50% 15%;
    --ring: 240 73% 65%;

    --sidebar-background: 220 65% 4%;
    --sidebar-foreground: 250 100% 98%;
    --sidebar-primary: 240 73% 65%;
    --sidebar-primary-foreground: 220 65% 4%;
    --sidebar-accent: 220 50% 10%;
    --sidebar-accent-foreground: 250 100% 98%;
    --sidebar-border: 220 50% 15%;
    --sidebar-ring: 240 73% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Storytelling-specific animations */
@layer utilities {
  .story-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
  }
  
  .magic-glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }
  
  .story-text {
    line-height: 1.8;
    font-size: 1.1rem;
  }
  
  .narrator-card {
    transition: all 0.3s ease;
  }
  
  .narrator-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }
}
