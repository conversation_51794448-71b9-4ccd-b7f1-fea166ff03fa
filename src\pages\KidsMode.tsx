
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Baby, 
  Play, 
  Pause, 
  Star, 
  Heart, 
  HelpCircle,
  Volume2,
  Award,
  Sparkles,
  MessageCircle
} from "lucide-react";
import { freeStories } from "@/data/dummyData";
import { toast } from "@/hooks/use-toast";

const KidsMode = () => {
  const [playingStory, setPlayingStory] = useState<string | null>(null);
  const [showQuiz, setShowQuiz] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [chatMode, setChatMode] = useState(false);
  const [chatMessages, setChatMessages] = useState<Array<{type: 'bot' | 'user', message: string}>>([]);

  const kidsFriendlyStories = freeStories.map(story => ({
    ...story,
    kidsTitle: story.title.replace(/The/g, "🌟 The"),
    kidsDescription: story.content.substring(0, 60) + "... 🎈",
    difficulty: Math.random() > 0.5 ? "Easy" : "Medium",
    ageGroup: "5-10 years"
  }));

  const quizQuestions = [
    {
      question: "What color was the fox in our story?",
      options: ["Orange", "Blue", "Green", "Purple"],
      correct: 0,
      emoji: "🦊"
    },
    {
      question: "Where did the adventure take place?",
      options: ["Space", "Ocean", "Forest", "City"],
      correct: 2,
      emoji: "🌲"
    },
    {
      question: "What did the characters learn about?",
      options: ["Flying", "Friendship", "Cooking", "Dancing"],
      correct: 1,
      emoji: "💖"
    }
  ];

  const playStory = (storyId: string, storyTitle: string) => {
    if (playingStory === storyId) {
      setPlayingStory(null);
      toast({
        title: "Story paused! ⏸️",
        description: "The magical story is taking a little break.",
      });
    } else {
      setPlayingStory(storyId);
      toast({
        title: `🎉 Playing: ${storyTitle}`,
        description: "Get ready for an amazing adventure!",
      });
    }
  };

  const startQuiz = () => {
    setShowQuiz(true);
    setCurrentQuestion(0);
    setScore(0);
    toast({
      title: "Quiz time! 🧠✨",
      description: "Let's see how well you listened to the story!",
    });
  };

  const answerQuestion = (selectedAnswer: number) => {
    const isCorrect = selectedAnswer === quizQuestions[currentQuestion].correct;
    
    if (isCorrect) {
      setScore(score + 1);
      toast({
        title: "Great job! 🌟",
        description: "You got it right! You're amazing!",
      });
    } else {
      toast({
        title: "Good try! 💪",
        description: "That's okay, keep learning and having fun!",
      });
    }

    if (currentQuestion < quizQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Quiz finished
      setTimeout(() => {
        setShowQuiz(false);
        const percentage = (score / quizQuestions.length) * 100;
        let message = "";
        if (percentage >= 80) {
          message = "You're a story superstar! 🌟⭐";
        } else if (percentage >= 60) {
          message = "Great listening! Keep it up! 👏";
        } else {
          message = "Good job trying! Practice makes perfect! 💪";
        }
        
        toast({
          title: `Quiz completed! Score: ${score}/${quizQuestions.length}`,
          description: message,
        });
      }, 1500);
    }
  };

  const startChat = () => {
    setChatMode(true);
    setChatMessages([
      { type: 'bot', message: "Hi there! 👋 I'm your story buddy! What would you like to talk about?" }
    ]);
  };

  const askChatQuestion = (question: string) => {
    setChatMessages(prev => [...prev, 
      { type: 'user', message: question },
      { type: 'bot', message: getRandomResponse() }
    ]);
  };

  const getRandomResponse = () => {
    const responses = [
      "That's a fantastic question! 🤔 Stories are full of surprises!",
      "Wow! You're so curious! 🌟 That reminds me of another adventure...",
      "Great thinking! 💭 What do you think happened next?",
      "You're so smart! 🧠 I love how you think about stories!",
      "That's exactly what I was thinking too! 🎉"
    ];
    return responses[Math.floor(Math.random() * responses.length)];
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Kids Mode Header */}
      <div className="text-center space-y-4 bg-gradient-to-r from-yellow-100 to-pink-100 rounded-2xl p-8">
        <h1 className="text-5xl font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
          Kids Story Time! 🎈
        </h1>
        <p className="text-2xl text-purple-700">
          Safe, fun, and magical stories just for you!
        </p>
        <div className="flex justify-center gap-4 mt-6">
          <Badge className="text-lg py-2 px-4 bg-green-500 text-white">
            👶 Kid Safe
          </Badge>
          <Badge className="text-lg py-2 px-4 bg-blue-500 text-white">
            🎓 Educational
          </Badge>
          <Badge className="text-lg py-2 px-4 bg-purple-500 text-white">
            🎉 Super Fun
          </Badge>
        </div>
      </div>

      {/* Interactive Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-blue-50 to-cyan-50">
          <CardHeader className="text-center">
            <div className="text-6xl mb-2">🧠</div>
            <CardTitle className="text-xl text-blue-700">Story Quiz</CardTitle>
            <CardDescription className="text-blue-600">
              Test what you learned from the stories!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={startQuiz} 
              className="w-full bg-blue-500 hover:bg-blue-600 text-white text-lg py-3"
              disabled={!playingStory}
            >
              <HelpCircle className="w-5 h-5 mr-2" />
              Start Quiz!
            </Button>
            {!playingStory && (
              <p className="text-xs text-muted-foreground mt-2 text-center">
                Listen to a story first!
              </p>
            )}
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-green-50 to-emerald-50">
          <CardHeader className="text-center">
            <div className="text-6xl mb-2">🤖</div>
            <CardTitle className="text-xl text-green-700">Story Buddy</CardTitle>
            <CardDescription className="text-green-600">
              Chat about stories with your AI friend!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={startChat} 
              className="w-full bg-green-500 hover:bg-green-600 text-white text-lg py-3"
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              Let's Chat!
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardHeader className="text-center">
            <div className="text-6xl mb-2">🏆</div>
            <CardTitle className="text-xl text-orange-700">Your Rewards</CardTitle>
            <CardDescription className="text-orange-600">
              Earn stars for listening and learning!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl">⭐⭐⭐</div>
              <p className="text-sm font-medium">You have 3 stars!</p>
              <Badge className="bg-yellow-500 text-white">
                <Award className="w-3 h-3 mr-1" />
                Story Explorer
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quiz Modal */}
      {showQuiz && (
        <Card className="fixed inset-4 z-50 bg-white shadow-2xl border-4 border-purple-200 mx-auto max-w-lg">
          <CardHeader className="text-center bg-gradient-to-r from-purple-100 to-pink-100">
            <CardTitle className="text-2xl text-purple-700">
              {quizQuestions[currentQuestion].emoji} Quiz Time!
            </CardTitle>
            <Progress value={(currentQuestion / quizQuestions.length) * 100} className="mt-2" />
          </CardHeader>
          <CardContent className="p-6 space-y-6">
            <h3 className="text-xl font-semibold text-center text-purple-800">
              {quizQuestions[currentQuestion].question}
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {quizQuestions[currentQuestion].options.map((option, index) => (
                <Button
                  key={index}
                  onClick={() => answerQuestion(index)}
                  className="text-lg py-4 bg-gradient-to-r from-purple-400 to-pink-400 hover:from-purple-500 hover:to-pink-500 text-white"
                >
                  {option}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chat Modal */}
      {chatMode && (
        <Card className="fixed inset-4 z-50 bg-white shadow-2xl border-4 border-green-200 mx-auto max-w-lg">
          <CardHeader className="text-center bg-gradient-to-r from-green-100 to-blue-100">
            <CardTitle className="text-2xl text-green-700">
              🤖 Story Buddy Chat
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 space-y-4 max-h-96 overflow-y-auto">
            {chatMessages.map((msg, index) => (
              <div key={index} className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs p-3 rounded-lg ${
                  msg.type === 'user' 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {msg.message}
                </div>
              </div>
            ))}
            <div className="grid grid-cols-1 gap-2 mt-4">
              <Button onClick={() => askChatQuestion("What was your favorite part?")}>
                What was your favorite part?
              </Button>
              <Button onClick={() => askChatQuestion("Can you tell me another story?")}>
                Can you tell me another story?
              </Button>
              <Button onClick={() => setChatMode(false)} variant="outline">
                Close Chat
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Kid-Friendly Stories */}
      <div className="space-y-6">
        <h2 className="text-3xl font-bold text-center text-purple-700">
          🌟 Stories Just for You! 🌟
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {kidsFriendlyStories.map((story) => (
            <Card key={story.id} className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-4 border-transparent hover:border-purple-200">
              <div className="aspect-video bg-gradient-to-r from-purple-300 to-pink-300 rounded-t-lg relative overflow-hidden">
                {story.thumbnail && (
                  <img 
                    src={story.thumbnail} 
                    alt={story.title}
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                  <Button
                    size="lg"
                    className="bg-white/90 hover:bg-white text-purple-700 text-xl py-4 px-6"
                    onClick={() => playStory(story.id, story.title)}
                  >
                    {playingStory === story.id ? (
                      <>
                        <Pause className="w-8 h-8 mr-2" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="w-8 h-8 mr-2" />
                        Play Story!
                      </>
                    )}
                  </Button>
                </div>
                {playingStory === story.id && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-green-500 text-white animate-pulse text-lg">
                      <Volume2 className="w-4 h-4 mr-1" />
                      Playing! 🎵
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardHeader>
                <CardTitle className="text-2xl text-purple-700">{story.kidsTitle}</CardTitle>
                <div className="flex gap-2 flex-wrap">
                  <Badge className="bg-purple-100 text-purple-700 text-sm">
                    {story.ageGroup}
                  </Badge>
                  <Badge className="bg-green-100 text-green-700 text-sm">
                    {story.difficulty}
                  </Badge>
                  <Badge className="bg-blue-100 text-blue-700 text-sm">
                    {story.duration} min
                  </Badge>
                </div>
                <CardDescription className="text-lg text-purple-600">
                  {story.kidsDescription}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🎙️ {story.narrator}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <span className="text-lg font-bold text-yellow-600">{story.likes}</span>
                  </div>
                </div>
                
                <Button 
                  className="w-full mt-4 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white text-xl py-4"
                  onClick={() => playStory(story.id, story.title)}
                >
                  {playingStory === story.id ? (
                    <>
                      <Pause className="w-6 h-6 mr-2" />
                      Pause Story 🛑
                    </>
                  ) : (
                    <>
                      <Play className="w-6 h-6 mr-2" />
                      Listen Now! 🎧
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Fun Learning Section */}
      <Card className="bg-gradient-to-r from-yellow-100 to-orange-100 border-4 border-yellow-200">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl text-orange-700">🎓 What Did You Learn Today?</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-4xl mb-2">🤝</div>
              <p className="text-sm font-medium text-orange-700">Friendship</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">💪</div>
              <p className="text-sm font-medium text-orange-700">Courage</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">🌟</div>
              <p className="text-sm font-medium text-orange-700">Kindness</p>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">🧠</div>
              <p className="text-sm font-medium text-orange-700">Wisdom</p>
            </div>
          </div>
          <p className="text-lg text-orange-600">
            Every story teaches us something amazing! Keep listening and learning! ✨
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default KidsMode;
