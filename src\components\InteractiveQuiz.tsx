import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  CheckCircle,
  XCircle,
  HelpCircle,
  Lightbulb,
  Trophy,
  Star
} from 'lucide-react';

export interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  hint?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface InteractiveQuizProps {
  questions: QuizQuestion[];
  onComplete: (score: number, answers: number[]) => void;
  title?: string;
  description?: string;
}

const InteractiveQuiz: React.FC<InteractiveQuizProps> = ({
  questions,
  onComplete,
  title = "Story Quiz",
  description = "Test your understanding of the story"
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [showExplanation, setShowExplanation] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [quizComplete, setQuizComplete] = useState(false);
  const [score, setScore] = useState(0);

  const currentQ = questions[currentQuestion];
  const progress = ((currentQuestion + 1) / questions.length) * 100;

  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestion] = answerIndex;
    setSelectedAnswers(newAnswers);
    setShowExplanation(true);

    // Calculate score
    if (answerIndex === currentQ.correctAnswer) {
      setScore(prev => prev + 1);
    }
  };

  const handleNext = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
      setShowExplanation(false);
      setShowHint(false);
    } else {
      setQuizComplete(true);
      onComplete(score, selectedAnswers);
    }
  };

  const handleHint = () => {
    setShowHint(true);
  };

  const getDifficultyColor = (difficulty: QuizQuestion['difficulty']) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreMessage = () => {
    const percentage = (score / questions.length) * 100;
    if (percentage >= 90) return { message: "Excellent! You're a story master! 🌟", icon: <Trophy className="w-6 h-6 text-yellow-500" /> };
    if (percentage >= 70) return { message: "Great job! You understood the story well! 👏", icon: <Star className="w-6 h-6 text-blue-500" /> };
    if (percentage >= 50) return { message: "Good work! Keep reading stories! 📚", icon: <CheckCircle className="w-6 h-6 text-green-500" /> };
    return { message: "Keep practicing! Every story teaches us something! 💪", icon: <HelpCircle className="w-6 h-6 text-purple-500" /> };
  };

  if (quizComplete) {
    const scoreMessage = getScoreMessage();
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {scoreMessage.icon}
          </div>
          <CardTitle className="text-2xl">Quiz Complete!</CardTitle>
          <CardDescription>{scoreMessage.message}</CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="text-4xl font-bold text-purple-600">
            {score}/{questions.length}
          </div>
          <div className="text-lg text-muted-foreground">
            {Math.round((score / questions.length) * 100)}% Correct
          </div>
          <Progress value={(score / questions.length) * 100} className="w-full" />
          <Button onClick={() => window.location.reload()} className="mt-4">
            Try Another Story
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="w-5 h-5" />
              {title}
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Badge className={getDifficultyColor(currentQ.difficulty)}>
            {currentQ.difficulty}
          </Badge>
        </div>
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Question {currentQuestion + 1} of {questions.length}</span>
            <span>Score: {score}/{currentQuestion + (showExplanation ? 1 : 0)}</span>
          </div>
          <Progress value={progress} className="w-full" />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">{currentQ.question}</h3>

          {showHint && currentQ.hint && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Lightbulb className="w-5 h-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="font-medium text-blue-800">Hint:</p>
                  <p className="text-blue-700">{currentQ.hint}</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-2">
            {currentQ.options.map((option, index) => {
              const isSelected = selectedAnswers[currentQuestion] === index;
              const isCorrect = index === currentQ.correctAnswer;
              const showResult = showExplanation;

              let buttonVariant: "default" | "outline" | "destructive" | "secondary" = "outline";
              let icon = null;

              if (showResult) {
                if (isCorrect) {
                  buttonVariant = "default";
                  icon = <CheckCircle className="w-4 h-4 text-green-500" />;
                } else if (isSelected && !isCorrect) {
                  buttonVariant = "destructive";
                  icon = <XCircle className="w-4 h-4 text-red-500" />;
                }
              }

              return (
                <Button
                  key={index}
                  variant={buttonVariant}
                  className={`w-full justify-start text-left h-auto p-4 ${
                    showResult && isCorrect ? 'bg-green-50 border-green-200 hover:bg-green-100' :
                    showResult && isSelected && !isCorrect ? 'bg-red-50 border-red-200 hover:bg-red-100' : ''
                  }`}
                  onClick={() => !showExplanation && handleAnswerSelect(index)}
                  disabled={showExplanation}
                >
                  <div className="flex items-center gap-3 w-full">
                    <span className="font-medium">{String.fromCharCode(65 + index)}.</span>
                    <span className="flex-1">{option}</span>
                    {icon}
                  </div>
                </Button>
              );
            })}
          </div>

          {showExplanation && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-800">Explanation:</p>
                  <p className="text-gray-700">{currentQ.explanation}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          {!showExplanation && currentQ.hint && (
            <Button variant="outline" onClick={handleHint} disabled={showHint}>
              <Lightbulb className="w-4 h-4 mr-2" />
              {showHint ? 'Hint Shown' : 'Show Hint'}
            </Button>
          )}

          {showExplanation && (
            <Button onClick={handleNext} className="ml-auto">
              {currentQuestion < questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default InteractiveQuiz;