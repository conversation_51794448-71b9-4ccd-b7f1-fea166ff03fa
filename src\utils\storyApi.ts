
import OpenAI from 'openai';

export interface StoryPayload {
  prompt: string;
  characters: Array<{
    name: string;
    traits: string[];
  }>;
  age_group: string;
  story_length: "short" | "medium" | "long";
  genre: string;
  generate_audio?: boolean;
}

export interface StoryResponse {
  id?: string;
  story: string;
  title: string;
  word_count: number;
  estimated_reading_time: number;
  age_group: string;
  characters_used: string[];
  created_at?: string;
  updated_at?: string;
  is_regenerated?: boolean;
  audio_base64?: string;
}

// External API response interface
interface ExternalStoryResponse {
  id: string;
  story: string;
  title: string;
  word_count: number;
  estimated_reading_time: number;
  age_group: string;
  characters_used: string[];
  created_at: string;
  updated_at: string;
  is_regenerated: boolean;
  audio_base64: string;
}

// Initialize OpenAI client only if API key is available
const getOpenAIClient = () => {
  const apiKey = import.meta.env.VITE_OPENAI_API_KEY;
  if (!apiKey) {
    return null;
  }
  return new OpenAI({
    apiKey: apiKey,
    dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
  });
};

// Generate story using external API
const generateStoryWithExternalAPI = async (payload: StoryPayload): Promise<StoryResponse> => {
  try {
    const apiUrl = 'https://storybot.onpointsoft.com/api/v1/generate-story';

    const requestBody = {
      prompt: payload.prompt,
      characters: payload.characters,
      age_group: payload.age_group,
      story_length: payload.story_length,
      genre: payload.genre,
      generate_audio: payload.generate_audio || true
    };

    console.log('Sending request to external API:', requestBody);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('External API error response:', errorText);
      throw new Error(`External API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data: ExternalStoryResponse = await response.json();
    console.log('External API response received:', {
      id: data.id,
      title: data.title,
      wordCount: data.word_count,
      hasAudio: !!data.audio_base64,
      audioLength: data.audio_base64?.length || 0
    });

    // Validate required fields
    if (!data.story || !data.title) {
      throw new Error('Invalid response from external API: missing story or title');
    }

    return {
      id: data.id,
      story: data.story,
      title: data.title,
      word_count: data.word_count,
      estimated_reading_time: data.estimated_reading_time,
      age_group: data.age_group,
      characters_used: data.characters_used || [],
      created_at: data.created_at,
      updated_at: data.updated_at,
      is_regenerated: data.is_regenerated,
      audio_base64: data.audio_base64
    };

  } catch (error) {
    console.error('Error with external story API:', error);
    throw error;
  }
};

export const generateStoryWithPayload = async (payload: StoryPayload): Promise<StoryResponse> => {
  try {
    // Try external API first
    console.log('Attempting to generate story with external API...');
    return await generateStoryWithExternalAPI(payload);

  } catch (externalError) {
    console.warn('External API failed, trying OpenAI fallback:', externalError);

    // Fallback to OpenAI if available
    const openai = getOpenAIClient();
    if (openai) {
      try {
        return await generateStoryWithOpenAI(payload, openai);
      } catch (openaiError) {
        console.warn('OpenAI also failed, using local fallback:', openaiError);
      }
    }

    // Final fallback to local generation
    console.warn('Using local fallback story generation');
    return generateFallbackStory(payload);
  }
};

// Separate OpenAI generation function
const generateStoryWithOpenAI = async (payload: StoryPayload, openai: OpenAI): Promise<StoryResponse> => {

  const { prompt, characters, age_group, story_length, genre } = payload;

  // Create character descriptions
  const characterDescriptions = characters.map(char =>
    `${char.name} (traits: ${char.traits.join(', ')})`
  ).join(', ');

  // Determine story length in words
  const lengthMap = {
    short: '300-500 words',
    medium: '500-800 words',
    long: '800-1200 words'
  };

  // Create the system prompt for story generation
  const systemPrompt = `You are a creative children's story writer. Create engaging, age-appropriate stories that are educational and entertaining. Always include positive messages about friendship, courage, kindness, and problem-solving.

Guidelines:
- Target age group: ${age_group}
- Story length: ${lengthMap[story_length]}
- Genre: ${genre}
- Include vivid descriptions and dialogue
- Ensure the story has a clear beginning, middle, and end
- Include a positive moral or lesson
- Make it engaging and magical

Return your response as a JSON object with the following structure:
{
  "title": "Story Title",
  "story": "The complete story text...",
  "characters_used": ["character1", "character2"]
}`;

  const userPrompt = `Create a story based on this prompt: "${prompt}"

${characters.length > 0 ? `Include these characters: ${characterDescriptions}` : ''}

Make it magical and engaging for children aged ${age_group}.`;

  const completion = await openai.chat.completions.create({
    model: "gpt-4",
    messages: [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ],
    temperature: 0.8,
    max_tokens: 2000,
  });

  const responseText = completion.choices[0]?.message?.content;
  if (!responseText) {
    throw new Error('No response from OpenAI');
  }

  // Parse the JSON response
  const parsedResponse = JSON.parse(responseText);

  // Calculate word count and reading time
  const wordCount = parsedResponse.story.split(/\s+/).length;
  const estimatedReadingTime = Math.ceil(wordCount / 100); // Assuming 100 words per minute for children

  return {
    story: parsedResponse.story,
    title: parsedResponse.title,
    word_count: wordCount,
    estimated_reading_time: estimatedReadingTime,
    age_group: age_group,
    characters_used: parsedResponse.characters_used || []
  };
};

// Fallback story generation for when API is not available
const generateFallbackStory = async (payload: StoryPayload): Promise<StoryResponse> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  const storyTemplates = [
    `In a magical land where ${payload.prompt.toLowerCase()}, there lived a brave young hero who discovered something extraordinary. The adventure began when they found a mysterious object that glowed with an inner light. As they touched it, the world around them transformed, revealing hidden powers they never knew they possessed. With newfound courage and the help of unexpected allies, they embarked on a quest that would change their life forever. Through challenges and triumphs, they learned that the greatest magic comes from believing in yourself and the power of friendship.`,

    `Once upon a time, ${payload.prompt.toLowerCase()} led to the most incredible adventure imaginable. Our hero, a curious and kind-hearted soul, stumbled upon a secret that had been hidden for centuries. As they delved deeper into this mystery, they encountered magical creatures, solved ancient puzzles, and discovered that they were part of a much larger story. With wisdom gained from each challenge and the support of new friends, they realized that every ending is just a new beginning in disguise.`,

    `Deep in a world where ${payload.prompt.toLowerCase()}, an unlikely hero emerged from the most ordinary of circumstances. What started as a simple day turned into an epic journey of discovery, courage, and wonder. Along the way, they met characters who taught them valuable lessons about kindness, perseverance, and the magic that exists in everyday moments. Through their adventures, they not only saved the day but also learned something precious about themselves.`
  ];

  const randomStory = storyTemplates[Math.floor(Math.random() * storyTemplates.length)];
  const wordCount = randomStory.split(/\s+/).length;

  return {
    story: randomStory,
    title: `The Adventure of ${payload.prompt}`,
    word_count: wordCount,
    estimated_reading_time: Math.ceil(wordCount / 100),
    age_group: payload.age_group,
    characters_used: payload.characters.map(c => c.name)
  };
};

export const createStoryPayload = (
  prompt: string,
  characters: Array<{ name: string; traits: string[] }>,
  ageGroup: string,
  storyLength: "short" | "medium" | "long",
  genre: string,
  generateAudio: boolean = true
): StoryPayload => {
  return {
    prompt,
    characters,
    age_group: ageGroup,
    story_length: storyLength,
    genre,
    generate_audio: generateAudio
  };
};
