import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>lider } from '@/components/ui/slider';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  Square, 
  Volume2, 
  VolumeX, 
  Rewind, 
  FastForward 
} from 'lucide-react';

interface SynchronizedStoryPlayerProps {
  audioUrl: string;
  storyText: string;
  title?: string;
  narrator?: string;
  className?: string;
}

const SynchronizedStoryPlayer: React.FC<SynchronizedStoryPlayerProps> = ({
  audioUrl,
  storyText,
  title,
  narrator,
  className = ''
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [highlightedWordIndex, setHighlightedWordIndex] = useState(-1);

  // Split story into words for highlighting
  const words = storyText.split(/(\s+)/).filter(word => word.trim().length > 0);
  const wordsPerSecond = 2.5; // Approximate reading speed

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => {
      const currentTime = audio.currentTime;
      setCurrentTime(currentTime);
      
      // Calculate which word should be highlighted based on time
      const wordsSpoken = Math.floor(currentTime * wordsPerSecond);
      setHighlightedWordIndex(Math.min(wordsSpoken, words.length - 1));
    };

    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      setHighlightedWordIndex(-1);
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl, words.length]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  const handlePlay = async () => {
    if (audioRef.current) {
      try {
        await audioRef.current.play();
        setIsPlaying(true);
      } catch (error) {
        console.error('Error playing audio:', error);
      }
    }
  };

  const handlePause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      setCurrentTime(0);
      setHighlightedWordIndex(-1);
    }
  };

  const handleSeek = (value: number[]) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value[0];
      setCurrentTime(value[0]);
      const wordsSpoken = Math.floor(value[0] * wordsPerSecond);
      setHighlightedWordIndex(Math.min(wordsSpoken, words.length - 1));
    }
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
    setIsMuted(false);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const skip = (seconds: number) => {
    if (audioRef.current) {
      const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const renderHighlightedText = () => {
    return words.map((word, index) => {
      const isHighlighted = index <= highlightedWordIndex;
      return (
        <span
          key={index}
          className={`transition-all duration-300 ${
            isHighlighted 
              ? 'bg-yellow-200 text-blue-800 font-semibold shadow-sm' 
              : 'text-gray-700'
          }`}
          style={{
            padding: '2px 1px',
            borderRadius: '3px',
          }}
        >
          {word}{' '}
        </span>
      );
    });
  };

  if (!audioUrl) {
    return (
      <div className="text-center p-4 text-gray-500">
        No audio available
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-2xl border-4 border-purple-200 shadow-lg ${className}`}>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-4 rounded-t-xl">
        <div className="text-center">
          <div className="text-3xl mb-2">🎙️</div>
          <h3 className="text-xl font-bold">{title || 'Story Narration'}</h3>
          {narrator && <p className="text-purple-100 text-sm">Narrated by {narrator}</p>}
        </div>
      </div>

      {/* Story Text with Highlighting */}
      <div className="p-6">
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-4 border-2 border-blue-200 mb-6">
          <h4 className="text-lg font-bold text-blue-700 mb-3 text-center">
            📖 Follow Along as the Story is Read
          </h4>
          <ScrollArea className="h-64 w-full">
            <div className="text-lg leading-relaxed p-2">
              {renderHighlightedText()}
            </div>
          </ScrollArea>
        </div>

        {/* Audio Controls */}
        <div className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
            <Slider
              value={[currentTime]}
              onValueChange={handleSeek}
              max={duration || 100}
              step={1}
              className="w-full"
              disabled={!duration}
            />
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => skip(-10)}
              disabled={!duration}
              className="bg-blue-50 hover:bg-blue-100"
            >
              <Rewind className="w-4 h-4" />
            </Button>

            <Button
              onClick={isPlaying ? handlePause : handlePlay}
              disabled={isLoading || !audioUrl}
              className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white px-6"
              size="lg"
            >
              {isLoading ? (
                <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : isPlaying ? (
                <Pause className="w-6 h-6" />
              ) : (
                <Play className="w-6 h-6" />
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => skip(10)}
              disabled={!duration}
              className="bg-blue-50 hover:bg-blue-100"
            >
              <FastForward className="w-4 h-4" />
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleStop}
              disabled={!duration}
              className="bg-red-50 hover:bg-red-100"
            >
              <Square className="w-4 h-4" />
            </Button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className="p-2"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume]}
              onValueChange={handleVolumeChange}
              max={1}
              step={0.1}
              className="flex-1 max-w-24"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SynchronizedStoryPlayer;
