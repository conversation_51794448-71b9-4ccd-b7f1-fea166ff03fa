﻿import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { 
  User, 
  Palette, 
  Heart, 
  Zap, 
  Shield, 
  Brain,
  Plus,
  Trash2,
  Shuffle
} from 'lucide-react';

export interface Character {
  id: string;
  name: string;
  role: string;
  appearance: {
    species: string;
    age: string;
    gender: string;
    hairColor: string;
    eyeColor: string;
    skinTone: string;
    height: string;
    build: string;
    outfit: string;
  };
  personality: {
    traits: string[];
    motivation: string;
    fears: string;
    quirks: string;
  };
  abilities: {
    strength: number;
    intelligence: number;
    creativity: number;
    kindness: number;
    courage: number;
  };
  backstory: string;
  avatar: string;
}

interface CharacterBuilderProps {
  characters: Character[];
  onCharactersChange: (characters: Character[]) => void;
  maxCharacters?: number;
}

const CharacterBuilder: React.FC<CharacterBuilderProps> = ({
  characters,
  onCharactersChange,
  maxCharacters = 5
}) => {
  const [selectedCharacter, setSelectedCharacter] = useState<string | null>(null);

  const speciesOptions = [
    { value: 'human', label: '👤 Human', emoji: '👤' },
    { value: 'animal', label: '🐾 Animal', emoji: '🐾' },
    { value: 'fantasy', label: '🧚 Fantasy Creature', emoji: '🧚' },
    { value: 'robot', label: '🤖 Robot', emoji: '🤖' },
    { value: 'alien', label: '👽 Alien', emoji: '👽' },
    { value: 'mythical', label: '🐉 Mythical Being', emoji: '🐉' }
  ];

  const personalityTraits = [
    'Brave', 'Kind', 'Funny', 'Smart', 'Creative', 'Loyal', 'Curious', 'Adventurous',
    'Gentle', 'Determined', 'Playful', 'Wise', 'Energetic', 'Caring', 'Mysterious', 'Cheerful'
  ];

  const outfitOptions = [
    'Casual clothes', 'Royal outfit', 'Adventure gear', 'Magical robes', 'School uniform',
    'Superhero costume', 'Medieval armor', 'Space suit', 'Pirate outfit', 'Fairy dress'
  ];

  const createNewCharacter = (): Character => ({
    id: Date.now().toString(),
    name: '',
    role: 'Hero',
    appearance: {
      species: 'human',
      age: 'child',
      gender: 'neutral',
      hairColor: 'brown',
      eyeColor: 'brown',
      skinTone: 'medium',
      height: 'average',
      build: 'average',
      outfit: 'casual clothes'
    },
    personality: {
      traits: ['Kind', 'Brave'],
      motivation: '',
      fears: '',
      quirks: ''
    },
    abilities: {
      strength: 50,
      intelligence: 50,
      creativity: 50,
      kindness: 50,
      courage: 50
    },
    backstory: '',
    avatar: '👤'
  });

  const addCharacter = () => {
    if (characters.length < maxCharacters) {
      const newCharacter = createNewCharacter();
      onCharactersChange([...characters, newCharacter]);
      setSelectedCharacter(newCharacter.id);
    }
  };

  const removeCharacter = (id: string) => {
    const updatedCharacters = characters.filter(char => char.id !== id);
    onCharactersChange(updatedCharacters);
    if (selectedCharacter === id) {
      setSelectedCharacter(updatedCharacters[0]?.id || null);
    }
  };

  const updateCharacter = (id: string, updates: Partial<Character>) => {
    const updatedCharacters = characters.map(char =>
      char.id === id ? { ...char, ...updates } : char
    );
    onCharactersChange(updatedCharacters);
  };

  const randomizeCharacter = (id: string) => {
    const character = characters.find(char => char.id === id);
    if (!character) return;

    const randomSpecies = speciesOptions[Math.floor(Math.random() * speciesOptions.length)];
    const randomTraits = personalityTraits
      .sort(() => 0.5 - Math.random())
      .slice(0, 3);
    const randomOutfit = outfitOptions[Math.floor(Math.random() * outfitOptions.length)];

    const updates: Partial<Character> = {
      appearance: {
        ...character.appearance,
        species: randomSpecies.value,
        outfit: randomOutfit
      },
      personality: {
        ...character.personality,
        traits: randomTraits
      },
      abilities: {
        strength: Math.floor(Math.random() * 100),
        intelligence: Math.floor(Math.random() * 100),
        creativity: Math.floor(Math.random() * 100),
        kindness: Math.floor(Math.random() * 100),
        courage: Math.floor(Math.random() * 100)
      },
      avatar: randomSpecies.emoji
    };

    updateCharacter(id, updates);
  };

  const selectedChar = characters.find(char => char.id === selectedCharacter);

  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="text-5xl mb-3 animate-bounce">🎭</div>
        <h3 className="text-2xl font-black text-purple-700 mb-2">Make Your Characters!</h3>
        <p className="text-lg font-bold text-purple-600 mb-4">
          Create awesome friends for your story!
        </p>
        <Button
          onClick={addCharacter}
          disabled={characters.length >= maxCharacters}
          className="bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 text-white font-black text-lg px-6 py-3 rounded-2xl shadow-lg transform transition-all hover:scale-105 disabled:opacity-50"
        >
          <div className="flex items-center gap-3">
            <Plus className="w-6 h-6" />
            <span>Add New Friend!</span>
            <div className="bg-white text-purple-600 px-2 py-1 rounded-full text-sm font-bold">
              {characters.length}/{maxCharacters}
            </div>
          </div>
        </Button>
      </div>

      {/* Character List - Child Friendly */}
      {characters.length > 0 && (
        <div className="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-2xl p-6 border-4 border-yellow-300">
          <div className="text-center mb-4">
            <div className="text-2xl font-black text-orange-700 mb-2">Your Amazing Characters! 🌟</div>
            <p className="text-lg font-bold text-orange-600">Click on a character to make them even cooler!</p>
          </div>
          <div className="flex gap-4 overflow-x-auto pb-2">
            {characters.map((character) => (
              <div
                key={character.id}
                className={`min-w-[140px] cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                  selectedCharacter === character.id
                    ? 'ring-8 ring-purple-400 shadow-2xl scale-105'
                    : 'hover:shadow-xl'
                }`}
                onClick={() => setSelectedCharacter(character.id)}
              >
                <div className="bg-gradient-to-br from-white to-purple-50 rounded-2xl p-4 text-center border-4 border-purple-200 shadow-lg">
                  <div className="text-5xl mb-2 animate-bounce">{character.avatar}</div>
                  <div className="text-lg font-black text-purple-700 truncate mb-1">
                    {character.name || '🤔 No Name Yet'}
                  </div>
                  <div className="text-sm font-bold text-purple-600 mb-3">
                    {character.role}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeCharacter(character.id);
                    }}
                    className="bg-red-100 hover:bg-red-200 text-red-600 font-bold rounded-full h-8 w-8 p-0 border-2 border-red-300"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Character Editor - Child Friendly */}
      {selectedChar && (
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 border-4 border-blue-200 shadow-xl">
          <div className="text-center mb-8">
            <div className="text-8xl mb-4 animate-pulse">{selectedChar.avatar}</div>
            <h3 className="text-3xl font-black text-blue-700 mb-2">
              {selectedChar.name || "Let's Name This Character!"}
            </h3>
            <Button
              onClick={() => randomizeCharacter(selectedChar.id)}
              className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white font-black text-lg px-6 py-3 rounded-2xl shadow-lg transform transition-all hover:scale-105"
            >
              <div className="flex items-center gap-3">
                <Shuffle className="w-6 h-6" />
                <span>🎲 Make It Random!</span>
              </div>
            </Button>
          </div>

          <div className="space-y-8">
            {/* Basic Info - Child Friendly */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-2xl p-6 border-4 border-green-200 shadow-lg">
                <div className="flex items-center gap-3 mb-4">
                  <div className="text-4xl">📝</div>
                  <Label className="text-xl font-bold text-green-700">
                    What's their name?
                  </Label>
                </div>
                <Input
                  value={selectedChar.name}
                  onChange={(e) => updateCharacter(selectedChar.id, { name: e.target.value })}
                  placeholder="Give them a cool name..."
                  className="text-lg font-semibold border-4 border-green-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-400 focus:border-green-400 bg-green-50 h-14"
                />
              </div>
              <div className="bg-white rounded-2xl p-6 border-4 border-blue-200 shadow-lg">
                <div className="flex items-center gap-3 mb-4">
                  <div className="text-4xl">🎭</div>
                  <Label className="text-xl font-bold text-blue-700">
                    What do they do in the story?
                  </Label>
                </div>
                <Select
                  value={selectedChar.role}
                  onValueChange={(value) => updateCharacter(selectedChar.id, { role: value })}
                >
                  <SelectTrigger className="text-lg font-semibold border-4 border-blue-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-400 focus:border-blue-400 bg-blue-50 h-14">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="border-4 border-blue-200 rounded-2xl">
                    <SelectItem value="Hero" className="text-lg font-semibold py-3">🦸 The Hero (Main Character!)</SelectItem>
                    <SelectItem value="Sidekick" className="text-lg font-semibold py-3">🤝 Best Friend Helper</SelectItem>
                    <SelectItem value="Mentor" className="text-lg font-semibold py-3">🧙 Wise Teacher</SelectItem>
                    <SelectItem value="Villain" className="text-lg font-semibold py-3">😈 The Bad Guy</SelectItem>
                    <SelectItem value="Friend" className="text-lg font-semibold py-3">👫 Good Friend</SelectItem>
                    <SelectItem value="Guide" className="text-lg font-semibold py-3">🗺️ Adventure Guide</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Appearance */}
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <Palette className="w-4 h-4" />
                Appearance
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Species</Label>
                  <Select
                    value={selectedChar.appearance.species}
                    onValueChange={(value) => {
                      const species = speciesOptions.find(s => s.value === value);
                      updateCharacter(selectedChar.id, {
                        appearance: { ...selectedChar.appearance, species: value },
                        avatar: species?.emoji || '👤'
                      });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {speciesOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Age</Label>
                  <Select
                    value={selectedChar.appearance.age}
                    onValueChange={(value) => updateCharacter(selectedChar.id, {
                      appearance: { ...selectedChar.appearance, age: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="child">Child</SelectItem>
                      <SelectItem value="teen">Teen</SelectItem>
                      <SelectItem value="adult">Adult</SelectItem>
                      <SelectItem value="elder">Elder</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Outfit</Label>
                  <Select
                    value={selectedChar.appearance.outfit}
                    onValueChange={(value) => updateCharacter(selectedChar.id, {
                      appearance: { ...selectedChar.appearance, outfit: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {outfitOptions.map(outfit => (
                        <SelectItem key={outfit} value={outfit}>
                          {outfit}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Personality */}
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <Heart className="w-4 h-4" />
                Personality
              </h4>
              <div className="space-y-2">
                <Label>Personality Traits</Label>
                <div className="flex flex-wrap gap-2">
                  {personalityTraits.map(trait => (
                    <Badge
                      key={trait}
                      variant={selectedChar.personality.traits.includes(trait) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        const currentTraits = selectedChar.personality.traits;
                        const newTraits = currentTraits.includes(trait)
                          ? currentTraits.filter(t => t !== trait)
                          : [...currentTraits, trait];
                        updateCharacter(selectedChar.id, {
                          personality: { ...selectedChar.personality, traits: newTraits }
                        });
                      }}
                    >
                      {trait}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Abilities */}
            <div className="space-y-4">
              <h4 className="font-semibold flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Abilities
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(selectedChar.abilities).map(([ability, value]) => (
                  <div key={ability} className="space-y-2">
                    <div className="flex justify-between">
                      <Label className="capitalize">{ability}</Label>
                      <span className="text-sm text-muted-foreground">{value}%</span>
                    </div>
                    <Slider
                      value={[value]}
                      onValueChange={([newValue]) => updateCharacter(selectedChar.id, {
                        abilities: { ...selectedChar.abilities, [ability]: newValue }
                      })}
                      max={100}
                      step={5}
                      className="w-full"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Backstory */}
            <div className="space-y-2">
              <Label>Backstory (Optional)</Label>
              <Textarea
                value={selectedChar.backstory}
                onChange={(e) => updateCharacter(selectedChar.id, { backstory: e.target.value })}
                placeholder="Tell us about this character's background..."
                rows={3}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CharacterBuilder;
