
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { Link } from "react-router-dom";
import { 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  Wand2,
  Eye,
  Sparkles
} from "lucide-react";
import { characters } from "@/data/dummyData";
import { toast } from "@/hooks/use-toast";

interface CustomCharacter {
  id: string;
  name: string;
  age: string;
  gender: string;
  appearance: string;
  personality: string;
  avatar: string;
  isCustom: boolean;
}

const CharacterStudio = () => {
  const { user } = useAuth();
  const [selected<PERSON>haracter, setSelected<PERSON>haracter] = useState<CustomCharacter | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [customCharacters, setCustomCharacters] = useState<CustomCharacter[]>([]);
  const [characterForm, setCharacterForm] = useState({
    name: "",
    age: "",
    gender: "",
    appearance: "",
    personality: "",
    avatar: "👤"
  });

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="text-6xl mb-4">🔒</div>
        <h2 className="text-2xl font-bold mb-4">Login Required</h2>
        <p className="text-muted-foreground mb-6">
          Please log in to access the Character Studio and create custom characters for your stories.
        </p>
        <Link to="/">
          <Button className="story-gradient text-white">
            Back to Home
          </Button>
        </Link>
      </div>
    );
  }

  const allCharacters = [...characters.map(c => ({ ...c, isCustom: false })), ...customCharacters];

  const avatarOptions = ["👤", "👨", "👩", "🧒", "👶", "🧓", "👴", "👵", "🦊", "🐱", "🐶", "🐻", "🦁", "🐼", "🐸", "🦄", "🐉", "🤖", "👸", "🤴", "🧙‍♂️", "🧙‍♀️", "🧚‍♂️", "🧚‍♀️"];

  const startCreating = () => {
    setIsCreating(true);
    setSelectedCharacter(null);
    setCharacterForm({
      name: "",
      age: "",
      gender: "",
      appearance: "",
      personality: "",
      avatar: "👤"
    });
  };

  const editCharacter = (character: CustomCharacter) => {
    if (!character.isCustom) return;
    setIsCreating(true);
    setSelectedCharacter(character);
    setCharacterForm({
      name: character.name,
      age: character.age,
      gender: character.gender,
      appearance: character.appearance,
      personality: character.personality,
      avatar: character.avatar
    });
  };

  const saveCharacter = () => {
    if (!characterForm.name.trim()) {
      toast({
        title: "Name required",
        description: "Please give your character a name!",
        variant: "destructive",
      });
      return;
    }

    const newCharacter: CustomCharacter = {
      id: selectedCharacter?.id || Date.now().toString(),
      ...characterForm,
      isCustom: true
    };

    if (selectedCharacter) {
      setCustomCharacters(prev => prev.map(c => c.id === selectedCharacter.id ? newCharacter : c));
      toast({
        title: "Character updated! ✨",
        description: `${newCharacter.name} has been updated successfully.`,
      });
    } else {
      setCustomCharacters(prev => [...prev, newCharacter]);
      toast({
        title: "Character created! 🎉",
        description: `${newCharacter.name} is ready for adventures!`,
      });
    }

    setIsCreating(false);
    setSelectedCharacter(null);
  };

  const deleteCharacter = (character: CustomCharacter) => {
    setCustomCharacters(prev => prev.filter(c => c.id !== character.id));
    toast({
      title: "Character deleted",
      description: `${character.name} has been removed from your collection.`,
    });
  };

  const generateRandomCharacter = () => {
    const names = ["Alex", "Sam", "Riley", "Jordan", "Casey", "Morgan", "Avery", "Taylor", "Quinn", "Sage"];
    const ages = ["Child", "Teenager", "Young Adult", "Adult", "Elder"];
    const genders = ["Male", "Female", "Non-binary"];
    const appearances = [
      "Bright golden hair that sparkles in sunlight",
      "Deep emerald eyes that seem to hold ancient wisdom",
      "A gentle smile that can light up any room",
      "Mysterious silver markings on their hands",
      "Rainbow-colored hair that changes with their mood"
    ];
    const personalities = [
      "Brave, curious, and always ready for adventure",
      "Kind, wise, and loves helping others",
      "Playful, energetic, and full of surprises",
      "Quiet, thoughtful, and incredibly clever",
      "Loyal, protective, and fiercely determined"
    ];

    setCharacterForm({
      name: names[Math.floor(Math.random() * names.length)],
      age: ages[Math.floor(Math.random() * ages.length)],
      gender: genders[Math.floor(Math.random() * genders.length)],
      appearance: appearances[Math.floor(Math.random() * appearances.length)],
      personality: personalities[Math.floor(Math.random() * personalities.length)],
      avatar: avatarOptions[Math.floor(Math.random() * avatarOptions.length)]
    });

    toast({
      title: "Random character generated! 🎲",
      description: "Feel free to customize them further!",
    });
  };

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent">
            Character Studio 🎭
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            Create and customize unique characters for your stories
          </p>
        </div>
        <Button onClick={startCreating} className="story-gradient text-white">
          <Plus className="w-4 h-4 mr-2" />
          Create Character
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Character Creation/Edit Panel */}
        {isCreating && (
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                {selectedCharacter ? 'Edit Character' : 'Create New Character'}
              </CardTitle>
              <CardDescription>
                Design a unique character for your magical stories
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Character Name</Label>
                  <Input
                    id="name"
                    value={characterForm.name}
                    onChange={(e) => setCharacterForm({ ...characterForm, name: e.target.value })}
                    placeholder="Enter character name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="age">Age Group</Label>
                  <Select value={characterForm.age} onValueChange={(value) => setCharacterForm({ ...characterForm, age: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select age group" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Child">Child</SelectItem>
                      <SelectItem value="Teenager">Teenager</SelectItem>
                      <SelectItem value="Young Adult">Young Adult</SelectItem>
                      <SelectItem value="Adult">Adult</SelectItem>
                      <SelectItem value="Elder">Elder</SelectItem>
                      <SelectItem value="Ancient">Ancient</SelectItem>
                      <SelectItem value="Timeless">Timeless</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender</Label>
                  <Select value={characterForm.gender} onValueChange={(value) => setCharacterForm({ ...characterForm, gender: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                      <SelectItem value="Non-binary">Non-binary</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Avatar</Label>
                  <div className="grid grid-cols-8 gap-2">
                    {avatarOptions.map((avatar) => (
                      <Button
                        key={avatar}
                        variant={characterForm.avatar === avatar ? "default" : "outline"}
                        className="w-10 h-10 p-0 text-lg"
                        onClick={() => setCharacterForm({ ...characterForm, avatar })}
                      >
                        {avatar}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="appearance">Appearance</Label>
                <Textarea
                  id="appearance"
                  value={characterForm.appearance}
                  onChange={(e) => setCharacterForm({ ...characterForm, appearance: e.target.value })}
                  placeholder="Describe how your character looks..."
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="personality">Personality</Label>
                <Textarea
                  id="personality"
                  value={characterForm.personality}
                  onChange={(e) => setCharacterForm({ ...characterForm, personality: e.target.value })}
                  placeholder="Describe your character's personality..."
                  rows={3}
                />
              </div>
              
              <div className="flex gap-3">
                <Button onClick={saveCharacter} className="story-gradient text-white">
                  <Save className="w-4 h-4 mr-2" />
                  {selectedCharacter ? 'Update Character' : 'Create Character'}
                </Button>
                <Button variant="outline" onClick={() => setIsCreating(false)}>
                  Cancel
                </Button>
                <Button variant="outline" onClick={generateRandomCharacter}>
                  <Wand2 className="w-4 h-4 mr-2" />
                  Random
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Character Preview */}
        {isCreating && (
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                <div className="text-6xl">{characterForm.avatar}</div>
                <div>
                  <h3 className="text-xl font-semibold">{characterForm.name || "Unnamed Character"}</h3>
                  <div className="flex justify-center gap-2 mt-2">
                    {characterForm.age && <Badge variant="secondary">{characterForm.age}</Badge>}
                    {characterForm.gender && <Badge variant="secondary">{characterForm.gender}</Badge>}
                  </div>
                </div>
                
                {characterForm.appearance && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">Appearance</h4>
                    <p className="text-sm">{characterForm.appearance}</p>
                  </div>
                )}
                
                {characterForm.personality && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">Personality</h4>
                    <p className="text-sm">{characterForm.personality}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Character Gallery */}
        <div className={isCreating ? "lg:col-span-3" : "lg:col-span-3"}>
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">Character Collection</h2>
            
            {/* Default Characters */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Featured Characters
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {characters.map((character) => (
                  <Card key={character.id} className="hover:shadow-md transition-all">
                    <CardContent className="p-4">
                      <div className="text-center space-y-3">
                        <div className="text-4xl">{character.avatar}</div>
                        <div>
                          <h3 className="font-semibold">{character.name}</h3>
                          <div className="flex justify-center gap-1 mt-1">
                            <Badge variant="outline" className="text-xs">{character.age}</Badge>
                            <Badge variant="outline" className="text-xs">{character.gender}</Badge>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {character.personality}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Custom Characters */}
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Users className="w-5 h-5" />
                My Characters ({customCharacters.length})
              </h3>
              
              {customCharacters.length === 0 ? (
                <Card className="text-center py-8">
                  <CardContent>
                    <div className="text-4xl mb-2">🎭</div>
                    <p className="text-muted-foreground">
                      No custom characters yet. Create your first character!
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {customCharacters.map((character) => (
                    <Card key={character.id} className="hover:shadow-md transition-all">
                      <CardContent className="p-4">
                        <div className="text-center space-y-3">
                          <div className="text-4xl">{character.avatar}</div>
                          <div>
                            <h3 className="font-semibold">{character.name}</h3>
                            <div className="flex justify-center gap-1 mt-1">
                              <Badge variant="outline" className="text-xs">{character.age}</Badge>
                              <Badge variant="outline" className="text-xs">{character.gender}</Badge>
                            </div>
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {character.personality}
                          </p>
                          <div className="flex gap-2 justify-center pt-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => editCharacter(character)}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => deleteCharacter(character)}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CharacterStudio;
