# Story Generator API Integration

This document describes the integration of the external story generation API into the Kid Story Verse application.

## API Details

**Endpoint:** `https://storybot.onpointsoft.com/api/v1/generate-story`
**Method:** POST
**Content-Type:** application/json

## Request Format

```json
{
  "prompt": "A magical adventure in an enchanted forest",
  "characters": [
    {
      "name": "<PERSON>",
      "traits": ["brave", "curious", "magical"]
    }
  ],
  "age_group": "8-12",
  "story_length": "short",
  "genre": "fantasy",
  "generate_audio": true
}
```

## Response Format

```json
{
  "id": "62cc654c-9612-48e6-b1af-ebea2f5ebc21",
  "story": "<PERSON>, a boy with eyes like melted chocolate...",
  "title": "The Whispering Woods and Raj's Radiant Bloom",
  "word_count": 351,
  "estimated_reading_time": 4,
  "age_group": "8-12",
  "characters_used": ["<PERSON>"],
  "created_at": "2025-07-25T10:22:09.969621",
  "updated_at": "2025-07-25T10:22:09.969621",
  "is_regenerated": false,
  "audio_base64": "..."
}
```

## Integration Features

### 1. Primary API Integration
- The external API is now the primary method for story generation
- Automatic fallback to OpenAI API if external API fails
- Final fallback to local story templates if both APIs fail

### 2. Audio Integration
- Automatic audio generation when `generate_audio: true`
- Base64 audio data is converted to playable blob URLs
- Fallback to local TTS service if audio is not provided

### 3. Error Handling
- Comprehensive error handling with informative user messages
- Graceful degradation through multiple fallback layers
- Console logging for debugging

### 4. Memory Management
- Automatic cleanup of blob URLs to prevent memory leaks
- Proper resource management for audio files

## Testing

You can test the API integration directly from the browser console:

```javascript
// Test the story generation
testStoryGeneration().then(result => console.log('Test completed:', result))
```

## File Changes

### Modified Files:
1. `src/utils/storyApi.ts` - Added external API integration
2. `src/pages/StoryGenerator.tsx` - Updated to handle audio from API
3. `src/utils/testStoryApi.ts` - Added testing utilities

### Key Functions:
- `generateStoryWithExternalAPI()` - Handles external API calls
- `generateStoryWithOpenAI()` - OpenAI fallback
- `generateFallbackStory()` - Local template fallback
- `base64ToBlob()` - Converts base64 audio to playable format

## Usage Flow

1. User creates story with prompt and characters
2. System calls external API with story parameters
3. If successful, story and audio are returned
4. **NEW: Story Review Step** - User can review story, see API response, and regenerate
5. Audio is converted from base64 to blob URL for playback
6. User can listen to voice preview in the review step
7. If external API fails, system falls back to OpenAI
8. If OpenAI fails, system uses local story templates
9. User can continue to narration step for additional voice options

## New Story Review Step Features

### ✅ **Story Review & Regeneration**
- Complete story display with metadata (word count, reading time, age group)
- **Regenerate Story** button to create new versions
- Character list showing which characters were used in the story
- Toggle to show/hide complete API response details

### ✅ **Voice Preview Integration**
- Automatic audio playback if `audio_base64` is provided by API
- AudioPlayer component integrated for immediate listening
- Fallback message when no audio is available
- Seamless integration with existing TTS system

### ✅ **API Response Transparency**
- Toggle button to show complete API response
- Detailed metadata display (ID, creation time, regeneration status)
- Full JSON response viewer for debugging
- Audio availability indicator

## Error Scenarios

1. **External API Failure**: Falls back to OpenAI API
2. **OpenAI API Failure**: Falls back to local story generation
3. **Audio Processing Failure**: Falls back to TTS generation
4. **Network Issues**: Graceful error messages to user

The integration ensures a robust story generation experience with multiple fallback layers.
