
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "@/hooks/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { useSpeechSynthesis, useSpeechRecognition } from 'react-speech-kit';
import {
  Wand2,
  Play,
  RotateCcw,
  Save,
  Volume2,
  <PERSON><PERSON><PERSON>,
  Loader2,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "lucide-react";
import { narrators, storyPrompts } from "@/data/dummyData";
import NarratorSelector from "@/components/NarratorSelector";
import AudioPlayer from "@/components/AudioPlayer";
import SynchronizedStoryPlayer from "@/components/SynchronizedStoryPlayer";
import CharacterBuilder, { Character } from "@/components/CharacterBuilder";
import { generateStoryWithPayload, createStoryPayload, StoryResponse } from "@/utils/storyApi";
import ttsService from "@/utils/ttsService";
import voiceRecordingService from "@/utils/voiceRecording";
import videoGenerationService, { VideoGenerationResult, VideoScene } from "@/utils/videoGeneration";
// Import test utilities for development
import "@/utils/testStoryApi";

// Utility function to convert base64 to blob
const base64ToBlob = (base64: string, mimeType: string): Blob => {
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
};

// Story creation steps
type StoryStep = 'type' | 'input' | 'settings' | 'generation' | 'review' | 'narration' | 'video' | 'preview' | 'publish';

type StoryType = 'ai-generated' | 'manual-writing' | 'voice-to-story' | 'script-to-video';

type OutputType = 'audio' | 'video'|any;

interface StorySettings {
  title: string;
  prompt: string;
  category: string;
  length: "short" | "medium" | "long";
  tone: string;
  language: string;
  ageGroup: string;
}

const StoryGenerator = () => {
  const { user } = useAuth();

  // Step management
  const [currentStep, setCurrentStep] = useState<StoryStep>('type');
  const [storyType, setStoryType] = useState<StoryType>('ai-generated');
  const [outputType, setOutputType] = useState<OutputType>('');

  // Speech recognition setup
  const [speechText, setSpeechText] = useState('');
  const [baseText, setBaseText] = useState('');
  const [isManualSpeechActive, setIsManualSpeechActive] = useState(false);

  const { listen, listening, stop, supported } = useSpeechRecognition({
    onResult: (result: string) => {
      // Only process results if manually activated
      if (!isManualSpeechActive) return;

      // Update the speech text with the latest result
      setSpeechText(result);
      // Combine base text with current speech result
      const combinedText = baseText + (baseText ? ' ' : '') + result;
      setStorySettings(prev => ({
        ...prev,
        prompt: combinedText
      }));
    },
    onEnd: () => {
      // When speech ends, make the current text the new base
      if (isManualSpeechActive) {
        setBaseText(storySettings.prompt);
        setSpeechText('');
        setIsManualSpeechActive(false);
      }
    },
    onError: (error) => {
      console.error('Speech recognition error:', error);
      setIsManualSpeechActive(false);
    }
  });

  // Story settings
  const [storySettings, setStorySettings] = useState<StorySettings>({
    title: "",
    prompt: "",
    category: "Adventure",
    length: "medium",
    tone: "Funny",
    language: "English",
    ageGroup: "8-12"
  });

  // Characters state
  const [characters, setCharacters] = useState<Character[]>([]);

  // Story generation state
  const [generatedStory, setGeneratedStory] = useState<StoryResponse | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string>("");
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);
  const [showApiResponse, setShowApiResponse] = useState(false);

  // Available narrators
  const availableNarrators: any = [
    {
      id: "narrator-1",
      name: "John Smith",
      gender: "male",
      age: "Adult",
      avatar: "👨"
    },
    {
      id: "narrator-2",
      name: "Sarah Johnson",
      gender: "female",
      age: "Young Adult",
      avatar: "👩"
    },
    {
      id: "narrator-3",
      name: "Alex Thompson",
      gender: "other",
      age: "Teen",
      avatar: "🧑"
    }
  ];

  // Narrator selection state
  const [selectedNarrator, setSelectedNarrator] = useState(narrators[0].id);
  const [selectedNarrators, setSelectedNarrators] = useState<any>([]);

  // Video generation state
  const [videoResult, setVideoResult] = useState<VideoGenerationResult | null>(null);
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);
  const [videoScenes, setVideoScenes] = useState<VideoScene[]>([]);
  const [videoStyle, setVideoStyle] = useState<'animated-text' | 'background-images' | 'cinematic' | 'illustrated'>('animated-text');

  // Initialize with a default narrator if none selected
  useEffect(() => {
    if (selectedNarrators.length === 0 && availableNarrators.length > 0) {
      setSelectedNarrators([availableNarrators[0]]);
    }
  }, [selectedNarrators]);

  // Cleanup blob URLs to prevent memory leaks
  useEffect(() => {
    return () => {
      if (audioUrl && audioUrl.startsWith('blob:')) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  // Cleanup speech recognition when leaving input step or component unmounts
  useEffect(() => {
    return () => {
      if (listening) {
        stop();
        setIsManualSpeechActive(false);
      }
    };
  }, [currentStep, listening, stop]);

  // Stop speech recognition when navigating away from input step
  useEffect(() => {
    if (currentStep !== 'input' && listening) {
      stop();
      setIsManualSpeechActive(false);
    }
  }, [currentStep, listening, stop]);

  // Extract title from prompt if not explicitly set
  const extractTitleFromPrompt = (prompt: string): string => {
    // Look for common title patterns
    const titlePatterns = [
      /^"([^"]+)"/,  // "Title in quotes"
      /^([^.!?]+)[.!?]/,  // First sentence
      /^([^,]+),/,  // First clause before comma
    ];

    for (const pattern of titlePatterns) {
      const match = prompt.match(pattern);
      if (match && match[1].length < 50) {
        return match[1].trim();
      }
    }

    // Fallback: use first few words
    const words = prompt.split(' ').slice(0, 5);
    return words.join(' ') + (prompt.split(' ').length > 5 ? '...' : '');
  };

  // Generate default characters based on story prompt and category
  const generateDefaultCharacters = (prompt: string, category: string) => {
    const defaultCharacterSets = {
      Adventure: [
        { name: "Alex", traits: ["brave", "curious", "adventurous"] },
        { name: "Maya", traits: ["clever", "loyal", "resourceful"] }
      ],
      Fantasy: [
        { name: "Luna", traits: ["magical", "wise", "kind"] },
        { name: "Finn", traits: ["brave", "determined", "loyal"] }
      ],
      "Fairy Tale": [
        { name: "Rose", traits: ["kind", "gentle", "brave"] },
        { name: "Jack", traits: ["clever", "adventurous", "helpful"] }
      ],
      "Sci-Fi": [
        { name: "Zara", traits: ["intelligent", "curious", "brave"] },
        { name: "Kai", traits: ["tech-savvy", "loyal", "creative"] }
      ],
      Mystery: [
        { name: "Detective Sam", traits: ["observant", "clever", "determined"] },
        { name: "Riley", traits: ["curious", "brave", "analytical"] }
      ],
      Friendship: [
        { name: "Emma", traits: ["kind", "loyal", "caring"] },
        { name: "Noah", traits: ["funny", "supportive", "brave"] }
      ],
      default: [
        { name: "Hero", traits: ["brave", "kind", "determined"] },
        { name: "Friend", traits: ["loyal", "helpful", "caring"] }
      ]
    };

    // Get characters for the category, or use default
    const categoryCharacters = defaultCharacterSets[category as keyof typeof defaultCharacterSets]
      || defaultCharacterSets.default;

    // If the prompt mentions specific names or characters, try to extract them
    const promptLower = prompt.toLowerCase();
    const mentionedNames = [];

    // Simple name extraction (you could make this more sophisticated)
    const commonNames = ['alex', 'maya', 'sam', 'emma', 'noah', 'luna', 'finn', 'rose', 'jack', 'zara', 'kai', 'riley'];
    for (const name of commonNames) {
      if (promptLower.includes(name)) {
        mentionedNames.push(name.charAt(0).toUpperCase() + name.slice(1));
      }
    }

    // If we found names in the prompt, use those with appropriate traits
    if (mentionedNames.length > 0) {
      return mentionedNames.slice(0, 2).map(name => ({
        name,
        traits: ["brave", "curious", "kind"]
      }));
    }

    // Otherwise, return the default characters for the category
    return categoryCharacters.slice(0, 2);
  };
  const generateStory = async () => {
    if (!storySettings.prompt.trim()) {
      toast({
        title: "Please enter a story prompt",
        description: "Tell us what kind of story you'd like to create!",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedStory(null);

    // Clean up previous audio URL if it's a blob
    if (audioUrl && audioUrl.startsWith('blob:')) {
      URL.revokeObjectURL(audioUrl);
    }
    setAudioUrl("");

    try {
      // Process characters for the API
      const processedCharacters = characters
        .filter(char => char.name && char.name.trim()) // Only include characters with names
        .map((char) => ({
          name: char.name.trim(),
          traits: [
            char.role,
            ...char.personality.traits.slice(0, 2) // Include up to 2 personality traits
          ].filter(Boolean)
        }));

      // If no characters are created, add some default characters based on the story prompt
      const finalCharacters = processedCharacters.length > 0
        ? processedCharacters
        : generateDefaultCharacters(storySettings.prompt, storySettings.category);

      // Extract title if not provided
      const finalTitle = storySettings.title.trim() || extractTitleFromPrompt(storySettings.prompt);

      // Log character information for debugging
      console.log('Characters for story generation:', {
        userCreated: processedCharacters.length,
        finalCharacters: finalCharacters,
        usingDefaults: processedCharacters.length === 0,
        extractedTitle: finalTitle
      });

      // Create story payload with audio generation enabled
      const payload = createStoryPayload(
        storySettings.prompt,
        finalCharacters,
        storySettings.ageGroup,
        storySettings.length,
        storySettings.category,
        true // Enable audio generation
      );

      // Generate story using AI
      const storyResponse = await generateStoryWithPayload(payload);
      setGeneratedStory(storyResponse);

      // Check if audio was generated by the external API
      if (storyResponse.audio_base64 && storyResponse.audio_base64.trim()) {
        // Convert base64 audio to blob URL
        try {
          const audioBlob = base64ToBlob(storyResponse.audio_base64, 'audio/mpeg');
          const audioUrl = URL.createObjectURL(audioBlob);
          setAudioUrl(audioUrl);

          toast({
            title: "Story and audio generated! ✨🎙️",
            description: "Your magical story is ready with voice narration!",
          });
        } catch (audioError) {
          console.error('Error processing audio:', audioError);
          toast({
            title: "Story generated! ✨",
            description: "Story created successfully! Audio processing failed, but you can generate it separately.",
          });

          // Fallback to TTS generation
          if (storyResponse.story) {
            generateAudio(storyResponse.story, storyResponse.title);
          }
        }
      } else {
        // No audio from external API, use TTS fallback
        toast({
          title: "Story generated! ✨",
          description: "Your magical story is ready! Generating audio...",
        });

        if (storyResponse.story) {
          generateAudio(storyResponse.story, storyResponse.title);
        }
      }

    } catch (error) {
      console.error('Error generating story:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast({
        title: "Story generation failed",
        description: `There was an error creating your story: ${errorMessage}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const regenerateStory = async () => {
    if (!storySettings.prompt.trim()) {
      toast({
        title: "Cannot regenerate story",
        description: "Story prompt is missing. Please go back and add a prompt.",
        variant: "destructive",
      });
      return;
    }

    setIsRegenerating(true);

    // Clean up previous audio URL if it's a blob
    if (audioUrl && audioUrl.startsWith('blob:')) {
      URL.revokeObjectURL(audioUrl);
    }
    setAudioUrl("");

    try {
      // Process characters for the API (same logic as generateStory)
      const processedCharacters = characters
        .filter(char => char.name && char.name.trim())
        .map((char) => ({
          name: char.name.trim(),
          traits: [
            char.role,
            ...char.personality.traits.slice(0, 2)
          ].filter(Boolean)
        }));

      const finalCharacters = processedCharacters.length > 0
        ? processedCharacters
        : generateDefaultCharacters(storySettings.prompt, storySettings.category);

      console.log('Regenerating story with characters:', finalCharacters);

      const payload = createStoryPayload(
        storySettings.prompt,
        finalCharacters,
        storySettings.ageGroup,
        storySettings.length,
        storySettings.category,
        true
      );

      const storyResponse = await generateStoryWithPayload(payload);
      setGeneratedStory(storyResponse);

      // Handle audio from API response
      if (storyResponse.audio_base64 && storyResponse.audio_base64.trim()) {
        try {
          const audioBlob = base64ToBlob(storyResponse.audio_base64, 'audio/mpeg');
          const audioUrl = URL.createObjectURL(audioBlob);
          setAudioUrl(audioUrl);

          toast({
            title: "Story regenerated! ✨🎙️",
            description: "Your new magical story is ready with voice narration!",
          });
        } catch (audioError) {
          console.error('Error processing regenerated audio:', audioError);
          toast({
            title: "Story regenerated! ✨",
            description: "New story created! Audio processing failed, but you can generate it separately.",
          });
        }
      } else {
        toast({
          title: "Story regenerated! ✨",
          description: "Your new magical story is ready!",
        });
      }

    } catch (error) {
      console.error('Error regenerating story:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      toast({
        title: "Story regeneration failed",
        description: `There was an error creating your new story: ${errorMessage}. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsRegenerating(false);
    }
  };

  const generateAudio = async (storyText: string, title?: string) => {
    setIsGeneratingAudio(true);

    try {
      const selectedNarratorData = narrators.find(n => n.id === selectedNarrator);
      const voiceName = selectedNarratorData?.voice || selectedNarratorData?.name || 'default';

      const ttsResponse = await ttsService.generateSpeech({
        text: storyText,
        voice: voiceName,
        speed: 1.0,
        pitch: 1.0
      });

      if (ttsResponse.success && ttsResponse.audioUrl) {
        setAudioUrl(ttsResponse.audioUrl);
        toast({
          title: "Audio generated! 🎙️",
          description: `${selectedNarratorData?.name || 'Narrator'} is ready to tell your story!`,
        });
      } else {
        throw new Error(ttsResponse.error || 'TTS generation failed');
      }
    } catch (error) {
      console.error('Error generating audio:', error);
      toast({
        title: "Audio generation failed",
        description: "Story will be available as text only. You can try generating audio again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingAudio(false);
    }
  };

  const generateVideo = async () => {
    if (!generatedStory) return;

    setIsGeneratingVideo(true);

    try {
      // Generate scenes from story
      const scenes = await videoGenerationService.generateScenes(generatedStory.story, videoStyle);
      setVideoScenes(scenes);

      // Generate video
      const videoOptions = {
        title: generatedStory.title || 'Story Video',
        scenes,
        audioUrl,
        style: videoStyle,
        dimensions: { width: 1920, height: 1080 },
        fps: 30,
        quality: 'medium' as const
      };

      const result = await videoGenerationService.generateVideo(videoOptions);
      setVideoResult(result);

      if (result.success) {
        toast({
          title: "Video generated! 🎥",
          description: "Your story video is ready to watch!",
        });
      } else {
        throw new Error(result.error || 'Video generation failed');
      }

    } catch (error) {
      console.error('Error generating video:', error);
      toast({
        title: "Video generation failed",
        description: "There was an error creating your video. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingVideo(false);
    }
  };

  const saveStory = async () => {
    if (!user) {
      toast({
        title: "Please log in to save stories",
        description: "Create an account to keep your magical stories safe!",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsSaving(false);
    
    toast({
      title: "Story saved! 📚",
      description: "Your story has been added to your personal collection.",
    });
  };

  const useRandomPrompt = () => {
    const randomPrompt = storyPrompts[Math.floor(Math.random() * storyPrompts.length)];
    setStorySettings(prev => ({ ...prev, prompt: randomPrompt }));
  };

  const selectedNarratorData = narrators.find(n => n.id === selectedNarrator);

  // Step navigation
  const steps: { key: StoryStep; title: string; description: string }[] = [
    { key: 'type', title: 'Story Type', description: 'Choose how to create your story' },
    { key: 'input', title: 'Story Input', description: 'Enter your story idea' },
    { key: 'settings', title: 'Customize', description: 'Set story preferences' },
    { key: 'generation', title: 'Generate', description: 'AI creates your story' },
    { key: 'review', title: 'Review', description: 'Review & edit your story' },
    { key: 'narration', title: 'Voice Narration', description: 'Add voice narration' },
    ...(outputType === 'video' ? [{ key: 'video' as StoryStep, title: 'Video', description: 'Create video' }] : []),
    { key: 'preview', title: 'Preview', description: 'Final preview' },
    { key: 'publish', title: 'Publish', description: 'Save and share' }
  ];

  const currentStepIndex = steps.findIndex(step => step.key === currentStep);

  const nextStep = () => {
    // Validation for each step
    switch (currentStep) {
      case 'type':
        if (!storyType) {
          toast({
            title: "Please select a story type! 🎭",
            description: "Choose how you want to create your amazing story!",
            variant: "destructive",
          });
          return;
        }
        if (!outputType) {
          toast({
            title: "Please choose output type! 🎯",
            description: "Select whether you want audio or video output!",
            variant: "destructive",
          });
          return;
        }
        break;

      case 'input':
        if (!storySettings.prompt.trim()) {
          toast({
            title: "Story idea is missing! 💭",
            description: "Please tell us your amazing story idea by typing or speaking!",
            variant: "destructive",
          });
          return;
        }
        if (storySettings.prompt.trim().length < 10) {
          toast({
            title: "Story idea is too short! 📝",
            description: "Please add more details to your story idea (at least 10 characters)!",
            variant: "destructive",
          });
          return;
        }
        break;

      case 'settings':
        if (!storySettings.category) {
          toast({
            title: "Please select a story category! 🎪",
            description: "Choose what kind of story you want to create!",
            variant: "destructive",
          });
          return;
        }
        if (!storySettings.length) {
          toast({
            title: "Please select story length! 📏",
            description: "Choose how long you want your story to be!",
            variant: "destructive",
          });
          return;
        }
        if (!storySettings.tone) {
          toast({
            title: "Please select story tone! 🎭",
            description: "Choose what feeling you want for your story!",
            variant: "destructive",
          });
          return;
        }
        if (!storySettings.ageGroup) {
          toast({
            title: "Please select age group! 👶",
            description: "Tell us your age so we can make the perfect story!",
            variant: "destructive",
          });
          return;
        }
        break;

      case 'generation':
        if (!generatedStory) {
          toast({
            title: "Please generate your story first! ✨",
            description: "Click the 'Generate Story' button to create your magical story!",
            variant: "destructive",
          });
          return;
        }
        break;

      case 'review':
        // No specific validation needed for review step
        break;

      case 'narration':
        if (outputType === 'video' && !audioUrl) {
          toast({
            title: "Audio required for video! 🎙️",
            description: "Please generate voice narration first for your video story!",
            variant: "destructive",
          });
          return;
        }
        break;

      case 'video':
        if (outputType === 'video' && !videoResult) {
          toast({
            title: "Please generate video! 🎥",
            description: "Create your video before proceeding to preview!",
            variant: "destructive",
          });
          return;
        }
        break;

      default:
        break;
    }

    // If validation passes, proceed to next step
    const nextIndex = Math.min(currentStepIndex + 1, steps.length - 1);
    let nextStepKey = steps[nextIndex].key;

    // Skip video step if audio output type is selected
    if (nextStepKey === 'video' && outputType === 'audio') {
      const skipVideoIndex = Math.min(nextIndex + 1, steps.length - 1);
      nextStepKey = steps[skipVideoIndex].key;
    }

    setCurrentStep(nextStepKey);

    // Success toast for completing a step
    const stepTitles = {
      'type': 'Story type selected! 🎭',
      'input': 'Story idea captured! 💭',
      'settings': 'Story customized! ⚙️',
      'generation': 'Story generated! ✨',
      'review': 'Story reviewed! 📖',
      'narration': 'Voice narration ready! 🎙️',
      'video': 'Video created! 🎥',
      'preview': 'Story preview complete! 👀'
    };

    if (stepTitles[currentStep as keyof typeof stepTitles]) {
      toast({
        title: stepTitles[currentStep as keyof typeof stepTitles],
        description: "Great job! Moving to the next step! 🚀",
      });
    }
  };

  const prevStep = () => {
    const prevIndex = Math.max(currentStepIndex - 1, 0);
    setCurrentStep(steps[prevIndex].key);
  };

  // Step 1: Choose Story Type - Child Friendly
  const renderStoryTypeStep = () => (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 border-4 border-blue-200 shadow-xl">
      <div className="text-center mb-8">
        <div className="text-6xl mb-4">🎪</div>
        <h2 className="text-4xl font-black text-blue-700 mb-2">
          How do you want to make your story?
        </h2>
        <p className="text-xl font-bold text-blue-600">
          Pick your favorite way! 🌈
        </p>
      </div>

      {/* Output Type Toggle */}
      <div className="mb-8">
        <div className="text-center mb-4">
          <h3 className="text-2xl font-black text-purple-700 mb-2">
            What do you want to create? ✨
          </h3>
          <p className="text-lg font-bold text-purple-600">
            Choose your output type first!
          </p>
        </div>
        <div className="flex justify-center gap-4">
          <div
            className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              outputType === 'audio'
                ? 'ring-6 ring-green-400 shadow-2xl scale-105'
                : 'hover:shadow-xl'
            }`}
            onClick={() => setOutputType('audio')}
          >
            <div className={` ${outputType === 'audio' ?'bg-gradient-to-br from-green-200 to-emerald-400 text-white':'bg-blue-200 text-gray-700'} rounded-2xl p-6 text-center  shadow-lg min-w-[200px]`}>
              <div className="text-6xl mb-3 animate-pulse">🎙️</div>
              <h4 className="text-xl font-black mb-2">Audio Story</h4>
              <p className="text-sm font-semibold">Story with voice narration!</p>
            </div>
          </div>
          <div
            className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              outputType === 'video'
                ? 'ring-6 ring-red-400  scale-105'
                : ''
            }`}
            onClick={() => setOutputType('video')}
          >
            <div className={` ${outputType === 'video' ?'bg-gradient-to-br from-red-200 to-red-400 text-white':'bg-blue-200 text-gray-700'} rounded-2xl p-6 text-center  shadow-lg min-w-[200px]`}>
              <div className="text-6xl mb-3 animate-pulse">🎥</div>
              <h4 className="text-xl font-black mb-2">Video Story</h4>
              <p className="text-sm font-semibold">Story with visuals & audio!</p>
            </div>
          </div>
        </div>
      </div>

      {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div
          className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
            storyType === 'ai-generated'
              ? 'ring-8 ring-purple-400 shadow-2xl scale-105'
              : 'hover:shadow-xl'
          }`}
          onClick={() => setStoryType('ai-generated')}
        >
          <div className="bg-gradient-to-br from-purple-400 to-pink-400 rounded-3xl p-8 text-center text-white shadow-lg">
            <div className="text-8xl mb-4 animate-pulse">🧠</div>
            <h3 className="text-2xl font-black mb-3">Magic AI Stories!</h3>
            <p className="text-lg font-semibold">Tell me your idea and I'll make it magical! ✨</p>
          </div>
        </div>

        <div
          className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
            storyType === 'manual-writing'
              ? 'ring-8 ring-green-400 shadow-2xl scale-105'
              : 'hover:shadow-xl'
          }`}
          onClick={() => setStoryType('manual-writing')}
        >
          <div className="bg-gradient-to-br from-green-400 to-emerald-400 rounded-3xl p-8 text-center text-white shadow-lg">
            <div className="text-8xl mb-4 animate-bounce">📝</div>
            <h3 className="text-2xl font-black mb-3">Write Together!</h3>
            <p className="text-lg font-semibold">You write, I help make it awesome! 🌟</p>
          </div>
        </div>

        <div
          className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
            storyType === 'voice-to-story'
              ? 'ring-8 ring-orange-400 shadow-2xl scale-105'
              : 'hover:shadow-xl'
          }`}
          onClick={() => setStoryType('voice-to-story')}
        >
          <div className="bg-gradient-to-br from-orange-400 to-red-400 rounded-3xl p-8 text-center text-white shadow-lg">
            <div className="text-8xl mb-4 animate-pulse">🎙️</div>
            <h3 className="text-2xl font-black mb-3">Talk Your Story!</h3>
            <p className="text-lg font-semibold">Just talk and I'll turn it into a story! 🎵</p>
            <div className="bg-yellow-300 text-yellow-800 px-3 py-1 rounded-full text-sm font-bold mt-2 inline-block">
              🎤 Voice Magic!
            </div>
          </div>
        </div>

        <div
          className={`cursor-pointer transition-all duration-300 transform hover:scale-105 ${
            storyType === 'script-to-video'
              ? 'ring-8 ring-blue-400 shadow-2xl scale-105'
              : 'hover:shadow-xl'
          }`}
          onClick={() => setStoryType('script-to-video')}
        >
          <div className="bg-gradient-to-br from-blue-400 to-indigo-400 rounded-3xl p-8 text-center text-white shadow-lg">
            <div className="text-8xl mb-4 animate-bounce">🎥</div>
            <h3 className="text-2xl font-black mb-3">Movie Stories!</h3>
            <p className="text-lg font-semibold">Make your story into a movie! 🍿</p>
            <div className="bg-yellow-300 text-yellow-800 px-3 py-1 rounded-full text-sm font-bold mt-2 inline-block">
              ⭐ Super Cool!
            </div>
          </div>
        </div>
      </div> */}

      <div className="text-center">
        <Button
          onClick={nextStep}
          disabled={!storyType}
          className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-black text-xl px-8 py-4 rounded-2xl shadow-lg transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          size="lg"
        >
          <div className="flex items-center gap-3">
            <span>Let's Go!</span>
            <div className="text-2xl">🚀</div>
          </div>
        </Button>
      </div>
    </div>
  );

  // Step 2: Story Input - Child Friendly
  const renderStoryInputStep = () => (
    <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-3xl p-8 border-4 border-green-200 shadow-xl">
      <div className="text-center mb-8">
        <div className="text-6xl mb-4 animate-bounce">💭</div>
        <h2 className="text-4xl font-black text-green-700 mb-2">
          Tell me your amazing story idea!
        </h2>
        <p className="text-xl font-bold text-green-600">
          Type or speak your story idea - I love hearing your creativity! 🌟
        </p>
      </div>

      <div className="space-y-8">
        {/* Combined Story Input with STT */}
        <div className="bg-white rounded-2xl p-6 border-4 border-blue-200 shadow-lg">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-3xl">💡</div>
            <Label htmlFor="story-input" className="text-xl font-bold text-blue-700">
              Your Story Idea & Title
            </Label>
          </div>

          <div className="space-y-4">
            <Textarea
              id="story-input"
              placeholder="Tell me your amazing story idea! You can include a title and describe what happens in your story..."
              value={storySettings.prompt}
              onChange={(e) => {
                const newValue = e.target.value;
                setStorySettings(prev => ({ ...prev, prompt: newValue }));
                // Update base text when user types manually (but not when listening)
                if (!listening) {
                  setBaseText(newValue);
                }
              }}
              rows={6}
              className="text-lg font-semibold border-4 border-blue-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-400 focus:border-blue-400 bg-blue-50 resize-none"
            />

            {/* Voice Input Controls */}
            <div className="flex items-center justify-center gap-4">
              <div className="text-center">
                <Button
                  type="button"
                  onClick={listening ? () => {
                    stop();
                    setIsManualSpeechActive(false);
                  } : () => {
                    // Set the current text as base before starting to listen
                    setBaseText(storySettings.prompt);
                    setIsManualSpeechActive(true);
                    listen();
                  }}
                  disabled={!supported}
                  className={`${
                    listening
                      ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700'
                      : 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
                  } text-white font-black text-lg px-8 py-4 rounded-2xl shadow-lg transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {listening ? (
                    <>
                      <Square className="w-6 h-6 mr-3" />
                      🛑 Stop Recording
                    </>
                  ) : (
                    <>
                      <Mic className="w-6 h-6 mr-3" />
                      {supported ? '🎤 Start Speaking' : '🎤 Voice Not Available'}
                    </>
                  )}
                </Button>
                {listening && (
                  <div className="mt-2 flex items-center justify-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-red-600 font-bold text-sm">Listening...</span>
                  </div>
                )}
                {!supported && (
                  <div className="mt-2">
                    <span className="text-orange-600 font-bold text-xs">
                      Voice input not supported in this browser
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm font-semibold text-blue-600">
                💡 Tip: You can type your story{supported ? ' or click the microphone to speak it' : ''}!
              </p>
            </div>
          </div>
        </div>

        {/* Random Prompt Button */}
        <div className="text-center">
          <Button
            onClick={useRandomPrompt}
            className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white font-black text-lg px-8 py-3 rounded-2xl shadow-lg transform transition-all hover:scale-105"
          >
            <div className="flex items-center justify-center gap-3">
              <div className="text-2xl">🎲</div>
              <span>Give Me a Random Idea!</span>
              <div className="text-2xl">✨</div>
            </div>
          </Button>
        </div>

        <div className="bg-white rounded-2xl p-6 border-4 border-pink-200 shadow-lg">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-3xl">🎪</div>
            <Label htmlFor="category" className="text-xl font-bold text-pink-700">
              What kind of story do you want?
            </Label>
          </div>
          <Select
            value={storySettings.category}
            onValueChange={(value) => setStorySettings(prev => ({ ...prev, category: value }))}
          >
            <SelectTrigger className="text-lg font-semibold border-4 border-pink-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-pink-400 focus:border-pink-400 bg-pink-50 h-14">
              <SelectValue placeholder="Pick your favorite type!" />
            </SelectTrigger>
            <SelectContent className="border-4 border-pink-200 rounded-2xl">
              <SelectItem value="Adventure" className="text-lg font-semibold py-3">🗺️ Adventure Stories</SelectItem>
              <SelectItem value="Fantasy" className="text-lg font-semibold py-3">🧚 Fantasy & Magic</SelectItem>
              <SelectItem value="Fairy Tale" className="text-lg font-semibold py-3">👑 Fairy Tales</SelectItem>
              <SelectItem value="Sci-Fi" className="text-lg font-semibold py-3">🚀 Space Adventures</SelectItem>
              <SelectItem value="Mystery" className="text-lg font-semibold py-3">🔍 Mystery & Puzzles</SelectItem>
              <SelectItem value="Friendship" className="text-lg font-semibold py-3">🤝 Friendship Stories</SelectItem>
              <SelectItem value="Educational" className="text-lg font-semibold py-3">📚 Learning Fun</SelectItem>
              <SelectItem value="Inspirational" className="text-lg font-semibold py-3">✨ Feel Good Stories</SelectItem>
              <SelectItem value="Myth" className="text-lg font-semibold py-3">🏛️ Ancient Legends</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-between items-center pt-4">
          <Button
            variant="outline"
            onClick={prevStep}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-bold text-lg px-6 py-3 rounded-2xl border-4 border-gray-300"
          >
            <div className="flex items-center gap-2">
              <span>👈</span>
              <span>Go Back</span>
            </div>
          </Button>
          <Button
            onClick={nextStep}
            disabled={!storySettings.prompt.trim()}
            className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-black text-lg px-8 py-3 rounded-2xl shadow-lg transform transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="flex items-center gap-3">
              <span>Next Step!</span>
              <div className="text-2xl">🚀</div>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );

  // Step 3: Story Settings - Child Friendly
  const renderStorySettingsStep = () => (
    <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-3xl p-8 border-4 border-yellow-200 shadow-xl">
      <div className="text-center mb-8">
        <div className="text-6xl mb-4 animate-spin">⚙️</div>
        <h2 className="text-4xl font-black text-yellow-700 mb-2">
          Let's make your story perfect!
        </h2>
        <p className="text-xl font-bold text-yellow-600">
          Choose what makes your story special! 🌟
        </p>
      </div>

      <div className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-2xl p-6 border-4 border-blue-200 shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-4xl">📏</div>
              <Label htmlFor="length" className="text-xl font-bold text-blue-700">
                How long should your story be?
              </Label>
            </div>
            <Select
              value={storySettings.length}
              onValueChange={(value: "short" | "medium" | "long") =>
                setStorySettings(prev => ({ ...prev, length: value }))
              }
            >
              <SelectTrigger className="text-lg font-semibold border-4 border-blue-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-blue-400 focus:border-blue-400 bg-blue-50 h-14">
                <SelectValue placeholder="Pick a size!" />
              </SelectTrigger>
              <SelectContent className="border-4 border-blue-200 rounded-2xl">
                <SelectItem value="short" className="text-lg font-semibold py-3">📖 Quick Story (Short & Sweet!)</SelectItem>
                <SelectItem value="medium" className="text-lg font-semibold py-3">📚 Perfect Story (Just Right!)</SelectItem>
                <SelectItem value="long" className="text-lg font-semibold py-3">📜 Epic Story (Super Long!)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="bg-white rounded-2xl p-6 border-4 border-purple-200 shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-4xl">🎭</div>
              <Label htmlFor="tone" className="text-xl font-bold text-purple-700">
                What feeling do you want?
              </Label>
            </div>
            <Select
              value={storySettings.tone}
              onValueChange={(value) => setStorySettings(prev => ({ ...prev, tone: value }))}
            >
              <SelectTrigger className="text-lg font-semibold border-4 border-purple-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-purple-400 focus:border-purple-400 bg-purple-50 h-14">
                <SelectValue placeholder="Pick a mood!" />
              </SelectTrigger>
              <SelectContent className="border-4 border-purple-200 rounded-2xl">
                <SelectItem value="Funny" className="text-lg font-semibold py-3">😄 Super Funny & Silly</SelectItem>
                <SelectItem value="Dramatic" className="text-lg font-semibold py-3">🎭 Exciting & Dramatic</SelectItem>
                <SelectItem value="Emotional" className="text-lg font-semibold py-3">💝 Sweet & Heartwarming</SelectItem>
                <SelectItem value="Motivational" className="text-lg font-semibold py-3">💪 Inspiring & Strong</SelectItem>
                <SelectItem value="Mysterious" className="text-lg font-semibold py-3">🔮 Mysterious & Cool</SelectItem>
                <SelectItem value="Adventurous" className="text-lg font-semibold py-3">⚡ Action-Packed Fun</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-2xl p-6 border-4 border-green-200 shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-4xl">👶</div>
              <Label htmlFor="ageGroup" className="text-xl font-bold text-green-700">
                How old are you?
              </Label>
            </div>
            <Select
              value={storySettings.ageGroup}
              onValueChange={(value) => setStorySettings(prev => ({ ...prev, ageGroup: value }))}
            >
              <SelectTrigger className="text-lg font-semibold border-4 border-green-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-400 focus:border-green-400 bg-green-50 h-14">
                <SelectValue placeholder="Pick your age!" />
              </SelectTrigger>
              <SelectContent className="border-4 border-green-200 rounded-2xl">
                <SelectItem value="3-5" className="text-lg font-semibold py-3">🍼 Little Kids (3-5 years)</SelectItem>
                <SelectItem value="6-8" className="text-lg font-semibold py-3">🎒 School Kids (6-8 years)</SelectItem>
                <SelectItem value="8-12" className="text-lg font-semibold py-3">📚 Big Kids (8-12 years)</SelectItem>
                <SelectItem value="12-16" className="text-lg font-semibold py-3">🎓 Teenagers (12-16 years)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="bg-white rounded-2xl p-6 border-4 border-red-200 shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="text-4xl">🌍</div>
              <Label htmlFor="language" className="text-xl font-bold text-red-700">
                What language do you speak?
              </Label>
            </div>
            <Select
              value={storySettings.language}
              onValueChange={(value) => setStorySettings(prev => ({ ...prev, language: value }))}
            >
              <SelectTrigger className="text-lg font-semibold border-4 border-red-200 rounded-2xl focus:outline-none focus:ring-4 focus:ring-red-400 focus:border-red-400 bg-red-50 h-14">
                <SelectValue placeholder="Pick your language!" />
              </SelectTrigger>
              <SelectContent className="border-4 border-red-200 rounded-2xl">
                <SelectItem value="English" className="text-lg font-semibold py-3">🇺🇸 English</SelectItem>
                <SelectItem value="Hindi" className="text-lg font-semibold py-3">🇮🇳 Hindi</SelectItem>
                <SelectItem value="Marathi" className="text-lg font-semibold py-3">🇮🇳 Marathi</SelectItem>
                <SelectItem value="Spanish" className="text-lg font-semibold py-3">🇪🇸 Spanish</SelectItem>
                <SelectItem value="French" className="text-lg font-semibold py-3">🇫🇷 French</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Character Builder - Child Friendly */}
        <div className="bg-gradient-to-br from-pink-100 to-purple-100 rounded-2xl p-6 border-4 border-pink-200 shadow-lg">
          <div className="text-center mb-6">
            <div className="text-5xl mb-3">👥</div>
            <h3 className="text-2xl font-black text-pink-700 mb-2">Create Your Story Characters!</h3>
            <p className="text-lg font-bold text-pink-600">Make cool characters for your story!</p>
            {characters.length === 0 && (
              <div className="bg-blue-100 border-2 border-blue-300 rounded-xl p-3 mt-4">
                <div className="text-blue-700 font-semibold text-sm">
                  💡 Don't worry! If you skip this, we'll create awesome characters for you automatically! ✨
                </div>
              </div>
            )}
          </div>
          <CharacterBuilder
            characters={characters}
            onCharactersChange={setCharacters}
            maxCharacters={3}
          />
        </div>

        <div className="flex justify-between items-center pt-4">
          <Button
            variant="outline"
            onClick={prevStep}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 font-bold text-lg px-6 py-3 rounded-2xl border-4 border-gray-300"
          >
            <div className="flex items-center gap-2">
              <span>👈</span>
              <span>Go Back</span>
            </div>
          </Button>
          <Button
            onClick={nextStep}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-black text-lg px-8 py-3 rounded-2xl shadow-lg transform transition-all hover:scale-105"
          >
            <div className="flex items-center gap-3">
              <span>All Set!</span>
              <div className="text-2xl">🎉</div>
            </div>
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      <div className="text-center space-y-6 bg-gradient-to-br from-purple-100 via-pink-50 to-blue-100 p-8 rounded-3xl shadow-lg">
        <div className="text-8xl animate-bounce">🎭</div>
        <h1 className="text-5xl font-black bg-gradient-to-r from-purple-600 via-pink-500 to-blue-600 bg-clip-text text-transparent">
          Story Magic! ✨
        </h1>
        <p className="text-2xl font-bold text-purple-700">
          Let's create amazing stories together! 🌟
        </p>

        {/* API Status Info - Child Friendly */}
        <div className="bg-green-100 border-4 border-green-300 rounded-2xl p-4 max-w-2xl mx-auto">
          <div className="flex items-center gap-3">
            <div className="text-3xl">🤖</div>
            <div className="text-left">
              <div className="font-bold text-green-800 text-lg">AI Story Magic Active! ✨</div>
              <div className="text-green-700">Using our super smart story robot to create amazing tales with voices!</div>
            </div>
          </div>
        </div>

        {!import.meta.env.VITE_OPENAI_API_KEY && (
          <div className="bg-blue-100 border-4 border-blue-300 rounded-2xl p-4 max-w-2xl mx-auto">
            <div className="flex items-center gap-3">
              <div className="text-3xl">🎪</div>
              <div className="text-left">
                <div className="font-bold text-blue-800 text-lg">Backup Magic Ready!</div>
                <div className="text-blue-700">If our robot gets tired, we have backup story magic ready!</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Progress Steps - Child Friendly */}
      <div className="bg-white rounded-3xl p-6 shadow-lg border-4 border-purple-200">
        <div className="text-center mb-4">
          <div className="text-2xl font-bold text-purple-700">Your Story Journey! 🗺️</div>
          <div className="text-lg text-purple-600">Step {currentStepIndex + 1} of {steps.length}</div>
        </div>
        <div className="flex items-center justify-center space-x-1 overflow-x-auto pb-2">
          {steps.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className={`flex flex-col items-center space-y-1 px-3 py-3 rounded-2xl text-center min-w-[80px] transition-all ${
                index === currentStepIndex
                  ? 'bg-gradient-to-br from-purple-400 to-pink-400 text-white font-bold shadow-lg scale-110'
                  : index < currentStepIndex
                    ? 'bg-gradient-to-br from-green-400 to-emerald-400 text-white font-semibold'
                    : 'bg-gray-100 text-gray-500'
              }`}>
                <div className="text-2xl">
                  {index === currentStepIndex ? '🌟' : index < currentStepIndex ? '✅' : '⭕'}
                </div>
                <span className="text-xs font-bold">{index + 1}</span>
                <span className="text-xs hidden sm:block font-medium">{step.title}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-4 h-1 mx-1 rounded-full ${
                  index < currentStepIndex ? 'bg-green-400' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="text-center mt-3">
          <div className="text-sm font-semibold text-purple-600">
            {steps[currentStepIndex].description} 🎯
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'type' && renderStoryTypeStep()}
      {currentStep === 'input' && renderStoryInputStep()}
      {currentStep === 'settings' && renderStorySettingsStep()}
      {currentStep === 'generation' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wand2 className="w-5 h-5" />
              Generate Your Story
            </CardTitle>
            <CardDescription>
              Ready to create your magical story?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <h3 className="font-semibold">Story Summary:</h3>
              <p><strong>Title:</strong> {storySettings.title || extractTitleFromPrompt(storySettings.prompt) || "Untitled Story"}</p>
              <p><strong>Story Idea:</strong> {storySettings.prompt}</p>
              <p><strong>Category:</strong> {storySettings.category}</p>
              <p><strong>Length:</strong> {storySettings.length}</p>
              <p><strong>Tone:</strong> {storySettings.tone}</p>
              <p><strong>Age Group:</strong> {storySettings.ageGroup}</p>
            </div>

            <div className="space-y-2">
              <NarratorSelector
                selectedNarrators={selectedNarrators}
                onNarratorsChange={setSelectedNarrators}
                availableNarrators={availableNarrators}
              />
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Back
              </Button>
              <Button
                onClick={async () => {
                  await generateStory();
                  nextStep(); // This will now go to the review step
                }}
                disabled={isGenerating || !storySettings.prompt.trim()}
                className="story-gradient text-white"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Generating Story...
                  </>
                ) : (
                  <>
                    <Wand2 className="w-5 h-5 mr-2" />
                    Generate Story
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Story Review Step */}
      {currentStep === 'review' && generatedStory && (
        <Card className="magic-glow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                📖 Your Amazing Story is Ready!
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowApiResponse(!showApiResponse)}
                  className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-300"
                >
                  {showApiResponse ? '📖 Hide Details' : '🔍 Show API Response'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={regenerateStory}
                  disabled={isRegenerating}
                  className="bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-300"
                >
                  {isRegenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Regenerating...
                    </>
                  ) : (
                    <>
                      <RotateCcw className="w-4 h-4 mr-2" />
                      🎲 Generate New Story
                    </>
                  )}
                </Button>
              </div>
            </div>
            <CardDescription>
              ✨ Review your story • 🎲 Regenerate if you want a different version • 🔍 View API response details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Story Content */}
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border-4 border-blue-200">
              <div className="text-center mb-4">
                <h2 className="text-3xl font-black text-blue-700 mb-2">
                  {generatedStory.title}
                </h2>
                <div className="flex justify-center gap-4 text-sm font-semibold text-blue-600">
                  <span>📊 {generatedStory.word_count} words</span>
                  <span>⏱️ {generatedStory.estimated_reading_time} min read</span>
                  <span>👶 Age {generatedStory.age_group}</span>
                </div>
              </div>
              <ScrollArea className="h-64 w-full rounded-md border-2 border-blue-300 p-4 bg-white">
                <p className="story-text leading-relaxed text-gray-800">
                  {generatedStory.story}
                </p>
              </ScrollArea>
            </div>

            {/* Characters Used */}
            {generatedStory.characters_used && generatedStory.characters_used.length > 0 && (
              <div className="bg-green-50 rounded-2xl p-4 border-2 border-green-300">
                <h3 className="text-lg font-bold text-green-700 mb-2 flex items-center gap-2">
                  👥 Characters in Your Story
                </h3>
                <div className="flex flex-wrap gap-2">
                  {generatedStory.characters_used.map((character, index) => (
                    <Badge key={index} variant="secondary" className="bg-green-100 text-green-800 border-green-300">
                      {character}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* API Response Details */}
            {showApiResponse && (
              <div className="bg-gray-50 rounded-2xl p-4 border-2 border-gray-300">
                <h3 className="text-lg font-bold text-gray-700 mb-3 flex items-center gap-2">
                  🔍 API Response Details
                </h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Story ID:</span>
                      <p className="text-gray-800 font-mono text-xs break-all">{generatedStory.id || 'N/A'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Created:</span>
                      <p className="text-gray-800">{generatedStory.created_at ? new Date(generatedStory.created_at).toLocaleString() : 'N/A'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Audio Available:</span>
                      <p className="text-gray-800">{generatedStory.audio_base64 && generatedStory.audio_base64.trim() ? '✅ Yes' : '❌ No'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Regenerated:</span>
                      <p className="text-gray-800">{generatedStory.is_regenerated ? '✅ Yes' : '❌ No'}</p>
                    </div>
                  </div>

                  {/* Full JSON Response */}
                  <div className="bg-white p-3 rounded-lg border">
                    <span className="font-semibold text-gray-600 mb-2 block">Full API Response:</span>
                    <ScrollArea className="h-32 w-full">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(generatedStory, null, 2)}
                      </pre>
                    </ScrollArea>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                👈 Back to Generate
              </Button>
              <Button
                onClick={nextStep}
                className="story-gradient text-white"
                size="lg"
              >
                Continue to Voice
                <Volume2 className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Story Review Step */}
      {currentStep === 'review' && generatedStory && (
        <Card className="magic-glow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                📖 Your Amazing Story is Ready!
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowApiResponse(!showApiResponse)}
                  className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-300"
                >
                  {showApiResponse ? '📖 Hide Details' : '🔍 Show API Response'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={regenerateStory}
                  disabled={isRegenerating}
                  className="bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-300"
                >
                  {isRegenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Regenerating...
                    </>
                  ) : (
                    <>
                      <RotateCcw className="w-4 h-4 mr-2" />
                      🎲 Generate New Story
                    </>
                  )}
                </Button>
              </div>
            </div>
            <CardDescription>
              ✨ Review your story • 🎲 Regenerate if you want a different version • 🔍 View API response details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Story Content */}
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border-4 border-blue-200">
              <div className="text-center mb-4">
                <h2 className="text-3xl font-black text-blue-700 mb-2">
                  {generatedStory.title}
                </h2>
                <div className="flex justify-center gap-4 text-sm font-semibold text-blue-600">
                  <span>📊 {generatedStory.word_count} words</span>
                  <span>⏱️ {generatedStory.estimated_reading_time} min read</span>
                  <span>👶 Age {generatedStory.age_group}</span>
                </div>
              </div>
              <ScrollArea className="h-64 w-full rounded-md border-2 border-blue-300 p-4 bg-white">
                <p className="story-text leading-relaxed text-gray-800">
                  {generatedStory.story}
                </p>
              </ScrollArea>
            </div>



            {/* Characters Used */}
            {generatedStory.characters_used && generatedStory.characters_used.length > 0 && (
              <div className="bg-green-50 rounded-2xl p-4 border-2 border-green-300">
                <h3 className="text-lg font-bold text-green-700 mb-2 flex items-center gap-2">
                  👥 Characters in Your Story
                </h3>
                <div className="flex flex-wrap gap-2">
                  {generatedStory.characters_used.map((character, index) => (
                    <Badge key={index} variant="secondary" className="bg-green-100 text-green-800 border-green-300">
                      {character}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* API Response Details */}
            {showApiResponse && (
              <div className="bg-gray-50 rounded-2xl p-4 border-2 border-gray-300">
                <h3 className="text-lg font-bold text-gray-700 mb-3 flex items-center gap-2">
                  🔍 API Response Details
                </h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Story ID:</span>
                      <p className="text-gray-800 font-mono text-xs break-all">{generatedStory.id || 'N/A'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Created:</span>
                      <p className="text-gray-800">{generatedStory.created_at ? new Date(generatedStory.created_at).toLocaleString() : 'N/A'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Audio Available:</span>
                      <p className="text-gray-800">{generatedStory.audio_base64 && generatedStory.audio_base64.trim() ? '✅ Yes' : '❌ No'}</p>
                    </div>
                    <div className="bg-white p-3 rounded-lg border">
                      <span className="font-semibold text-gray-600">Regenerated:</span>
                      <p className="text-gray-800">{generatedStory.is_regenerated ? '✅ Yes' : '❌ No'}</p>
                    </div>
                  </div>

                  {/* Full JSON Response */}
                  <div className="bg-white p-3 rounded-lg border">
                    <span className="font-semibold text-gray-600 mb-2 block">Full API Response:</span>
                    <ScrollArea className="h-32 w-full">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(generatedStory, null, 2)}
                      </pre>
                    </ScrollArea>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                👈 Back to Generate
              </Button>
              <Button
                onClick={nextStep}
                className="story-gradient text-white"
                size="lg"
              >
                Continue to Voice Narration
                <Volume2 className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Narration Step */}
      {currentStep === 'narration' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Volume2 className="w-5 h-5" />
              Voice Narration
            </CardTitle>
            <CardDescription>
              {audioUrl
                ? "🎉 Your story already has voice narration! Listen to the preview below or continue."
                : "Generate voice narration for your story and preview it with synchronized text highlighting"
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Synchronized Story Player */}
            {audioUrl && generatedStory && (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-purple-700 mb-2">
                    🎙️ Listen & Follow Along!
                  </h3>
                  <p className="text-lg text-purple-600 font-semibold">
                    Watch the text highlight as the story is narrated!
                  </p>
                </div>
                <SynchronizedStoryPlayer
                  audioUrl={audioUrl}
                  storyText={generatedStory.story}
                  title={generatedStory.title}
                  narrator="AI Narrator"
                />
                <div className="bg-green-100 border-2 border-green-300 rounded-xl p-4 text-center">
                  <p className="text-green-700 font-semibold">
                    ✨ Your story already has voice narration! You can generate a different voice below or continue to the next step.
                  </p>
                </div>
              </div>
            )}

            {/* Show story text even without audio */}
            {!audioUrl && generatedStory && (
              <div className="bg-blue-50 rounded-2xl p-6 border-2 border-blue-300">
                <div className="text-center mb-4">
                  <h3 className="text-2xl font-bold text-blue-700 mb-2">
                    📖 Your Story
                  </h3>
                  <p className="text-lg text-blue-600 font-semibold">
                    Generate voice narration to see synchronized highlighting!
                  </p>
                </div>
                <ScrollArea className="h-64 w-full rounded-md border-2 border-blue-300 p-4 bg-white">
                  <p className="story-text leading-relaxed text-gray-800">
                    {generatedStory.story}
                  </p>
                </ScrollArea>
              </div>
            )}

            {/* {selectedNarratorData && (
              <div className="space-y-4">
                <div className="text-center space-y-2">
                  <div className="text-6xl">{selectedNarratorData.avatar}</div>
                  <h3 className="text-xl font-semibold">{selectedNarratorData.name}</h3>
                  <div className="flex justify-center gap-2">
                    <Badge variant="secondary">{selectedNarratorData.gender}</Badge>
                    <Badge variant="secondary">{selectedNarratorData.age}</Badge>
                    <Badge variant="secondary">{selectedNarratorData.accent}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedNarratorData.description}
                  </p>
                </div>

                <Card className="bg-muted/50">
                  <CardContent className="p-4">
                    <p className="text-sm italic text-center story-text">
                      "{selectedNarratorData.previewText}"
                    </p>
                  </CardContent>
                </Card>

                <Button variant="outline" className="w-full">
                  <Play className="w-4 h-4 mr-2" />
                  🔁 Preview Voice
                </Button>
              </div>
            )} */}

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Back
              </Button>
              <Button
                onClick={() => {
                  if (generatedStory && !audioUrl) {
                    generateAudio(generatedStory.story, generatedStory.title);
                  }
                  nextStep();
                }}
                disabled={isGeneratingAudio}
                className="story-gradient text-white"
              >
                {isGeneratingAudio ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Generating Audio...
                  </>
                ) : audioUrl ? (
                  <>
                    Continue to {outputType === 'video' ? 'Video' : 'Preview'}
                    <Volume2 className="w-5 h-5 ml-2" />
                  </>
                ) : (
                  <>
                    <Volume2 className="w-5 h-5 mr-2" />
                    Generate Audio & Continue
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Video Step */}
      {currentStep === 'video' && outputType === 'video' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="w-5 h-5" />
              🎥 Create Video (Optional)
            </CardTitle>
            <CardDescription>
              Transform your story into a visual experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label>Visual Style</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-2">
                  {[
                    { value: 'animated-text', label: '📝 Animated Text', desc: 'Text with transitions' },
                    { value: 'background-images', label: '🖼️ Background Images', desc: 'Images with text overlay' },
                    { value: 'cinematic', label: '🎬 Cinematic', desc: 'Movie-style presentation' },
                    { value: 'illustrated', label: '🎨 Illustrated', desc: 'Hand-drawn style' }
                  ].map((style) => (
                    <Card
                      key={style.value}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        videoStyle === style.value ? 'ring-2 ring-purple-500' : ''
                      }`}
                      onClick={() => setVideoStyle(style.value as any)}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl mb-2">{style.label.split(' ')[0]}</div>
                        <div className="text-sm font-medium">{style.label.split(' ').slice(1).join(' ')}</div>
                        <div className="text-xs text-muted-foreground mt-1">{style.desc}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {videoResult && (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">Video Preview</h4>
                    <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                      <img
                        src={videoResult.thumbnailUrl}
                        alt="Video thumbnail"
                        className="max-w-full max-h-full rounded"
                      />
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground">
                      Duration: {Math.round(videoResult.duration)}s • {videoScenes.length} scenes
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Back
              </Button>
              <div className="flex gap-2">
                {!videoResult && (
                  <Button
                    onClick={generateVideo}
                    disabled={isGeneratingVideo}
                    className="story-gradient text-white"
                  >
                    {isGeneratingVideo ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        Generating Video...
                      </>
                    ) : (
                      <>
                        <Play className="w-5 h-5 mr-2" />
                        Generate Video
                      </>
                    )}
                  </Button>
                )}
                <Button onClick={nextStep}>
                  {videoResult ? 'Continue to Preview' : 'Skip Video'}
                  <Sparkles className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Preview Step */}
      {currentStep === 'preview' && generatedStory && (
        <Card className="magic-glow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                🧾 Review Your Story
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setGeneratedStory(null);
                    setCurrentStep('type');
                    setAudioUrl("");
                    setOutputType('audio');
                    setBaseText('');
                    setSpeechText('');
                    setIsManualSpeechActive(false);
                    if (listening) stop();
                  }}
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  New Story
                </Button>
              </div>
            </div>
            <CardDescription>
              ✏️ Edit title, tags, or story content • 🔊 Play narration • 🎥 Watch video preview
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <ScrollArea className="h-64 w-full rounded-md border p-4">
              <p className="story-text leading-relaxed">
                {generatedStory?.story}
              </p>
            </ScrollArea>

            {/* Synchronized Story Player */}
            {isGeneratingAudio ? (
              <Card className="w-full">
                <CardContent className="p-4">
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Generating audio narration...</span>
                  </div>
                </CardContent>
              </Card>
            ) : audioUrl && generatedStory ? (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-purple-700 mb-2">
                    🎙️ Final Story Preview
                  </h3>
                  <p className="text-lg text-purple-600 font-semibold">
                    Listen to your complete story with synchronized text highlighting!
                  </p>
                </div>
                <SynchronizedStoryPlayer
                  audioUrl={audioUrl}
                  storyText={generatedStory.story}
                  title={generatedStory.title}
                  narrator="AI Narrator"
                />
              </div>
            ) : (
              <div className="bg-orange-100 border-2 border-orange-300 rounded-xl p-4 text-center">
                <div className="text-orange-700 font-semibold">
                  🎵 No audio available for preview
                </div>
                <div className="text-orange-600 text-sm mt-1">
                  Go back to generate voice narration for your story.
                </div>
              </div>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Back to Narration
              </Button>
              <Button onClick={nextStep} className="story-gradient text-white">
                Continue to Publish
                <Sparkles className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Publish Step */}
      {currentStep === 'publish' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="w-5 h-5" />
              📥 Publish & Save Your Story
            </CardTitle>
            <CardDescription>
              ✅ Publish to library • 📥 Download • 🔗 Share
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={saveStory}
                disabled={isSaving || !user}
                className="story-gradient text-white h-20 flex-col"
              >
                {isSaving ? (
                  <Loader2 className="w-6 h-6 animate-spin" />
                ) : (
                  <>
                    <Save className="w-6 h-6 mb-2" />
                    Save to Library
                  </>
                )}
              </Button>

              <Button variant="outline" className="h-20 flex-col">
                <Volume2 className="w-6 h-6 mb-2" />
                Download MP3
              </Button>

              <Button variant="outline" className="h-20 flex-col">
                <Heart className="w-6 h-6 mb-2" />
                Share Story
              </Button>
            </div>

            {!user && (
              <Alert>
                <AlertDescription>
                  Please log in to save your story to your personal library.
                </AlertDescription>
              </Alert>
            )}

            <div className="flex justify-between">
              <Button variant="outline" onClick={prevStep}>
                Back to Preview
              </Button>
              <Button
                onClick={() => {
                  setCurrentStep('type');
                  setGeneratedStory(null);
                  setAudioUrl("");
                  setOutputType('audio');
                  setBaseText('');
                  setSpeechText('');
                  setIsManualSpeechActive(false);
                  if (listening) stop();
                  setStorySettings({
                    title: "",
                    prompt: "",
                    category: "Adventure",
                    length: "medium",
                    tone: "Funny",
                    language: "English",
                    ageGroup: "8-12"
                  });
                  setCharacters([]);
                }}
                className="story-gradient text-white"
              >
                Create Another Story
                <Sparkles className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      <Toaster />
    </div>
  );
};

export default StoryGenerator;
