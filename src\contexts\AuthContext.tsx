
import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  accountType: 'adult' | 'child';
  preferences: {
    favoriteNarrator: string;
    autoplay: boolean;
    kidsMode: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (email: string, password: string, name: string, accountType: 'adult' | 'child') => Promise<void>;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Dummy users for demonstration
const dummyUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Parent User',
    avatar: '👨‍👩‍👧‍👦',
    accountType: 'adult',
    preferences: {
      favoriteNarrator: 'Sarah',
      autoplay: true,
      kidsMode: false,
    },
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Little <PERSON>',
    avatar: '🧒',
    accountType: 'child',
    preferences: {
      favoriteNarrator: 'Charlie',
      autoplay: true,
      kidsMode: true,
    },
  },
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate checking for existing session
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const foundUser = dummyUsers.find(u => u.email === email);
    if (foundUser) {
      setUser(foundUser);
      localStorage.setItem('currentUser', JSON.stringify(foundUser));
    } else {
      throw new Error('Invalid credentials');
    }
    setIsLoading(false);
  };

  const register = async (email: string, password: string, name: string, accountType: 'adult' | 'child') => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newUser: User = {
      id: Date.now().toString(),
      email,
      name,
      avatar: accountType === 'child' ? '🧒' : '👤',
      accountType,
      preferences: {
        favoriteNarrator: 'Sarah',
        autoplay: true,
        kidsMode: accountType === 'child',
      },
    };
    
    setUser(newUser);
    localStorage.setItem('currentUser', JSON.stringify(newUser));
    setIsLoading(false);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, register, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
