
export interface Story {
  id: string;
  title: string;
  content: string;
  narrator: string;
  audioUrl?: string;
  videoUrl?: string;
  thumbnail?: string;
  tags: string[];
  createdAt: string;
  userId?: string;
  isFree: boolean;
  duration: number;
  likes: number;
  characters?: Character[];
}

export interface Character {
  id: string;
  name: string;
  age: string;
  gender: string;
  appearance: string;
  personality: string;
  avatar: string;
}

export interface Narrator {
  id: string;
  name: string;
  voice: string;
  gender: string;
  age: string;
  accent: string;
  description: string;
  avatar: string;
  previewText: string;
}

export const narrators: Narrator[] = [
  {
    id: "sarah",
    name: "<PERSON>",
    voice: "sarah-storyteller",
    gender: "Female",
    age: "Adult",
    accent: "American",
    description: "Warm and engaging, perfect for bedtime stories",
    avatar: "👩‍🏫",
    previewText: "Once upon a time, in a magical forest far, far away..."
  },
  {
    id: "charlie",
    name: "<PERSON>",
    voice: "charlie-child",
    gender: "Male",
    age: "Child",
    accent: "British",
    description: "Playful and enthusiastic, great for adventure tales",
    avatar: "👦",
    previewText: "Wow! Let me tell you about the most amazing adventure ever!"
  },
  {
    id: "grandma",
    name: "<PERSON> <PERSON>",
    voice: "rose-elderly",
    gender: "Female",
    age: "Elderly",
    accent: "Southern",
    description: "Gentle and wise, brings comfort to every story",
    avatar: "👵",
    previewText: "Come sit by me, dear child, and I'll share a wonderful tale..."
  },
  {
    id: "captain",
    name: "Captain Jack",
    voice: "jack-adventure",
    gender: "Male",
    age: "Adult",
    accent: "Australian",
    description: "Bold and adventurous, perfect for action stories",
    avatar: "🧑‍✈️",
    previewText: "Ahoy there, matey! Ready for an epic adventure on the high seas?"
  }
];

export const characters: Character[] = [
  {
    id: "fox",
    name: "Felix the Fox",
    age: "Young Adult",
    gender: "Male",
    appearance: "Bright orange fur with a white chest and black-tipped ears",
    personality: "Clever, curious, and kind-hearted",
    avatar: "🦊"
  },
  {
    id: "princess",
    name: "Princess Luna",
    age: "Teenager",
    gender: "Female",
    appearance: "Long silver hair that sparkles like starlight",
    personality: "Brave, compassionate, and magical",
    avatar: "👸"
  },
  {
    id: "dragon",
    name: "Spark the Dragon",
    age: "Ancient",
    gender: "Non-binary",
    appearance: "Emerald green scales with golden eyes",
    personality: "Wise, protective, and surprisingly gentle",
    avatar: "🐉"
  },
  {
    id: "robot",
    name: "Bolt the Robot",
    age: "Newly Created",
    gender: "Non-binary",
    appearance: "Shiny blue metal with glowing LED eyes",
    personality: "Logical, loyal, and learning about emotions",
    avatar: "🤖"
  }
];

export const freeStories: Story[] = [
  {
    id: "1",
    title: "The Magical Forest Adventure",
    content: "Deep in the enchanted forest, where sunbeams danced through emerald leaves, lived a curious little fox named Felix. One morning, Felix discovered a shimmering portal behind an ancient oak tree. The portal sparkled with colors he had never seen before - purples that sang like wind chimes and golds that tasted like honey. Without hesitation, Felix stepped through and found himself in a world where flowers grew upside down and butterflies painted rainbows in the sky. As he explored this wonder-filled realm, Felix met Luna, a kind princess who had been waiting for a brave friend to help her solve the mystery of the missing star songs. Together, they embarked on a quest that would teach them both about courage, friendship, and the magic that exists when we believe in ourselves.",
    narrator: "Sarah",
    audioUrl: "/audio/magical-forest.mp3",
    thumbnail: "https://images.unsplash.com/photo-1501854140801-50d01698950b?w=400",
    tags: ["Adventure", "Magic", "Friendship"],
    createdAt: "2024-01-15",
    isFree: true,
    duration: 8,
    likes: 342,
    characters: [characters[0], characters[1]]
  },
  {
    id: "2",
    title: "The Brave Little Robot",
    content: "In a bustling city of tomorrow, where flying cars zipped between crystal towers, there lived a small robot named Bolt. Unlike other robots who were programmed for specific tasks, Bolt was special - he was curious about everything! One day, while exploring the city's underground tunnels, Bolt discovered that all the city's power crystals were dimming. Without these crystals, the city would fall into darkness forever. Determined to help, Bolt set off on an incredible journey through abandoned subway systems, ancient computer cores, and even into the depths of cyberspace itself. Along the way, he learned that being different wasn't something to hide, but rather his greatest strength. With creativity and determination, Bolt found a way to restore the crystals and save his home, proving that even the smallest among us can make the biggest difference.",
    narrator: "Charlie",
    audioUrl: "/audio/brave-robot.mp3",
    thumbnail: "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400",
    tags: ["Technology", "Courage", "City"],
    createdAt: "2024-01-10",
    isFree: true,
    duration: 12,
    likes: 567,
    characters: [characters[3]]
  },
  {
    id: "3",
    title: "The Dragon's Garden",
    content: "High atop the misty mountains, where clouds brushed against ancient peaks, lived Spark, the gentlest dragon in all the land. Unlike the fearsome dragons of old tales, Spark spent their days tending to the most beautiful garden anyone had ever seen. Flowers bloomed in impossible colors, and vegetables grew as large as houses. But when a terrible drought threatened all the villages below, Spark faced a difficult choice. The garden's magic could save everyone, but using it would mean giving up the home they loved. With wisdom that came from centuries of caring for growing things, Spark decided to share the garden's seeds with all the villages, teaching everyone how to grow their own magical gardens. Soon, the entire land bloomed with color and life, proving that the greatest magic of all is kindness shared.",
    narrator: "Grandma Rose",
    audioUrl: "/audio/dragons-garden.mp3",
    thumbnail: "https://images.unsplash.com/photo-1439886183900-e79ec0057170?w=400",
    tags: ["Dragons", "Nature", "Kindness"],
    createdAt: "2024-01-05",
    isFree: true,
    duration: 10,
    likes: 423,
    characters: [characters[2]]
  }
];

export const userStories: Story[] = [
  {
    id: "4",
    title: "My Pet Dinosaur",
    content: "When Tommy found a strange egg in his backyard, he never expected it to hatch into a friendly baby dinosaur! Tiny, as Tommy named him, was no bigger than a house cat but had an enormous appetite for adventure. Together, they explored hidden caves, solved neighborhood mysteries, and learned that sometimes the best friends come in the most unexpected packages. But when Tiny began to grow larger each day, Tommy realized he would have to make the hardest decision of his life.",
    narrator: "Charlie",
    audioUrl: "/audio/pet-dinosaur.mp3",
    thumbnail: "https://images.unsplash.com/photo-1472396961693-142e6e269027?w=400",
    tags: ["Dinosaurs", "Friendship", "Growing Up"],
    createdAt: "2024-01-20",
    userId: "1",
    isFree: false,
    duration: 15,
    likes: 89,
    characters: []
  },
  {
    id: "5",
    title: "The Time-Traveling Backpack",
    content: "Sarah's grandmother's old backpack seemed ordinary until she discovered it could transport her through time! From ancient Egypt to the Wild West, from medieval castles to future space stations, Sarah learned about history firsthand. But when she accidentally changed something in the past, she had to race through time to set things right before her present world disappeared forever.",
    narrator: "Sarah",
    audioUrl: "/audio/time-backpack.mp3",
    thumbnail: "https://images.unsplash.com/photo-1500375592092-40eb2168fd21?w=400",
    tags: ["Time Travel", "History", "Adventure"],
    createdAt: "2024-01-18",
    userId: "1",
    isFree: false,
    duration: 20,
    likes: 156,
    characters: []
  }
];

export const storyPrompts = [
  "A brave little mouse goes on a quest to find the golden cheese",
  "Two best friends discover a secret portal in their school library",
  "A young wizard loses their magic wand and must find it before the spell runs out",
  "An alien child visits Earth for the first time and learns about human friendship",
  "A talking tree needs help saving the forest from a mysterious darkness",
  "A group of toys come to life when no one is watching",
  "A shy butterfly learns to overcome their fears to help their garden friends",
  "A pirate ship full of friendly animals searches for buried treasure",
  "A young inventor creates a machine that can talk to animals",
  "A magical paintbrush brings everything it paints to life"
];
