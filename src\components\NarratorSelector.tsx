import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, X } from "lucide-react";

interface Narrator {
  id: string;
  name: string;
  gender: string;
  age: string;
  avatar: string;
}

interface NarratorSelectorProps {
  selectedNarrators: Narrator[];
  onNarratorsChange: (narrators: Narrator[]) => void;
  availableNarrators?: Narrator[];
}

const NarratorSelector: React.FC<NarratorSelectorProps> = ({
  selectedNarrators,
  onNarratorsChange,
  availableNarrators = []
}) => {
  const [newNarratorName, setNewNarratorName] = useState("");
  const [newNarratorG<PERSON>, setNewNarratorGender] = useState("");
  const [newNarratorAge, setNewNarratorAge] = useState("");

  const genderOptions = [
    { value: "male", label: "Male" },
    { value: "female", label: "Female" },
    { value: "other", label: "Other" },
  ];

  const avatarEmojis = {
    male: "👨",
    female: "👩",
    other: "🧑"
  };

  const handleAddNarrator = () => {
    if (!newNarratorName.trim() || !newNarratorGender) return;

    const newNarrator: Narrator = {
      id: `narrator-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: newNarratorName.trim(),
      gender: newNarratorGender,
      age: newNarratorAge || "Adult",
      avatar: avatarEmojis[newNarratorGender as keyof typeof avatarEmojis] || "🧑"
    };

    onNarratorsChange([...selectedNarrators, newNarrator]);
    
    // Reset form
    setNewNarratorName("");
    setNewNarratorGender("");
    setNewNarratorAge("");
  };

  const handleRemoveNarrator = (narratorId: string) => {
    onNarratorsChange(selectedNarrators.filter(n => n.id !== narratorId));
  };

  const handleSelectExistingNarrator = (narratorId: string) => {
    const narrator = availableNarrators.find(n => n.id === narratorId);
    if (narrator && !selectedNarrators.find(n => n.id === narratorId)) {
      onNarratorsChange([...selectedNarrators, narrator]);
    }
  };

  return (
    <div className="space-y-4">
      <Label className="text-base font-medium">Choose Narrators</Label>
      
      {/* Existing Narrators Selection */}
      {availableNarrators.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm text-muted-foreground">Select from existing:</Label>
          <Select onValueChange={handleSelectExistingNarrator}>
            <SelectTrigger>
              <SelectValue placeholder="Choose an existing narrator..." />
            </SelectTrigger>
            <SelectContent>
              {availableNarrators
                .filter(narrator => !selectedNarrators.find(n => n.id === narrator.id))
                .map((narrator) => (
                  <SelectItem key={narrator.id} value={narrator.id}>
                    <div className="flex items-center gap-2">
                      <span>{narrator.avatar}</span>
                      <span>{narrator.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {narrator.gender} • {narrator.age}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Add New Narrator */}
      <div className="space-y-3">
        <Label className="text-sm text-muted-foreground">Or create a new narrator:</Label>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <div className="md:col-span-2">
            <Input
              placeholder="Narrator name..."
              value={newNarratorName}
              onChange={(e) => setNewNarratorName(e.target.value)}
            />
          </div>
          <div>
            <Select value={newNarratorGender} onValueChange={setNewNarratorGender}>
              <SelectTrigger>
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                {genderOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Input
              placeholder="Age (optional)"
              value={newNarratorAge}
              onChange={(e) => setNewNarratorAge(e.target.value)}
              className="flex-1"
            />
            <Button
              type="button"
              onClick={handleAddNarrator}
              disabled={!newNarratorName.trim() || !newNarratorGender}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Selected Narrators Display */}
      {selectedNarrators.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Selected Narrators:</Label>
          <div className="grid gap-2">
            {selectedNarrators.map((narrator) => (
              <Card key={narrator.id} className="p-3">
                <CardContent className="p-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{narrator.avatar}</span>
                      <div>
                        <p className="font-medium">{narrator.name}</p>
                        <div className="flex gap-2">
                          <Badge variant="outline" className="text-xs">
                            {narrator.gender}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {narrator.age}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveNarrator(narrator.id)}
                      className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NarratorSelector;
