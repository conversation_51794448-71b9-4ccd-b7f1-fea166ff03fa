
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { Header } from "@/components/Header";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import StoryGenerator from "./pages/StoryGenerator";
import FreeStories from "./pages/FreeStories";
import MyStories from "./pages/MyStories";
import VideoStudio from "./pages/VideoStudio";
import CharacterStudio from "./pages/CharacterStudio";
import KidsMode from "./pages/KidsMode";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <SidebarProvider>
            <div className="flex min-h-screen w-full bg-gradient-to-br from-background via-secondary/30 to-accent/20">
              <AppSidebar />
              <div className="flex-1 flex flex-col">
                <Header />
                <main className="flex-1 p-6">
                  <Routes>
                    <Route path="/" element={<Index />} />
                    <Route path="/generate" element={<StoryGenerator />} />
                    <Route path="/free-stories" element={<FreeStories />} />
                    <Route path="/my-stories" element={<MyStories />} />
                    <Route path="/video-studio" element={<VideoStudio />} />
                    <Route path="/character-studio" element={<CharacterStudio />} />
                    <Route path="/kids-mode" element={<KidsMode />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </main>
              </div>
            </div>
          </SidebarProvider>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
