import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { <PERSON>, useNavigate } from "react-router-dom";
import { 
  Wand2, 
  Heart, 
  BookOpen, 
  Video, 
  Users, 
  Baby, 
  Play,
  Clock,
  Star,
  Sparkles,
  ArrowRight,
  Zap,
  Crown,
  Rocket,
  Moon,
  Sun,
  Globe
} from "lucide-react";
import { freeStories } from "@/data/dummyData";
import ConfirmationDialog from "@/components/confirmationDialog";
// import { ParticleSystem } from "@/components/effects/ParticleSystem";
import { 
  MagicalContainer, 
  FloatingCard, 
  PulsingIcon, 
  ShimmerText, 
  MorphingButton 
} from "@/components/animations/MotionComponent";
// import { FamilyInteraction } from "@/components/interactive/FamilyInteraction";
// import { FamilyBackground } from "@/components/backgrounds/FamilyBackground";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";

const Index = () => {
  const { user } = useAuth();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [clickRipples, setClickRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const [isInteracting, setIsInteracting] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    const handleClick = (e: MouseEvent) => {
      const newRipple = {
        id: Date.now(),
        x: e.clientX,
        y: e.clientY
      };
      setClickRipples(prev => [...prev, newRipple]);
      setIsInteracting(true);
      
      // Remove ripple after animation
      setTimeout(() => {
        setClickRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
      }, 1000);
      
      // Reset interaction state
      setTimeout(() => setIsInteracting(false), 200);
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('click', handleClick);
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('click', handleClick);
    };
  }, []);

  const features = [
    {
      icon: Wand2,
      title: "AI Story Generation",
      description: "Create enchanting stories with cutting-edge AI technology and neural storytelling",
      path: "/",
      color: "from-purple-500 via-pink-500 to-purple-600",
      glow: "shadow-purple-500/30",
      hoverIcon: Sparkles
    },
    {
      icon: Heart,
      title: "Free Stories Library",
      description: "Discover thousands of magical tales curated by storytelling experts worldwide",
      path: "/free-stories",
      color: "from-red-500 via-rose-500 to-pink-500",
      glow: "shadow-rose-500/30",
      hoverIcon: Globe
    },
    {
      icon: Video,
      title: "Video Studio",
      description: "Transform stories into cinematic masterpieces with AI-powered animation",
      path: "/video-studio",
      color: "from-blue-500 via-cyan-500 to-teal-500",
      glow: "shadow-cyan-500/30",
      requiresAuth: true,
      hoverIcon: Play
    },
    {
      icon: Users,
      title: "Character Studio",
      description: "Design and customize unforgettable characters with advanced AI modeling",
      path: "/character-studio",
      color: "from-green-500 via-emerald-500 to-teal-500",
      glow: "shadow-emerald-500/30",
      requiresAuth: true,
      hoverIcon: Crown
    },
    {
      icon: Baby,
      title: "Kids Mode",
      description: "Safe, interactive storytelling experience designed for young imaginations",
      path: "/kids-mode",
      color: "from-yellow-500 via-orange-500 to-red-500",
      glow: "shadow-orange-500/30",
      hoverIcon: Sun
    },
    {
      icon: BookOpen,
      title: "My Stories",
      description: "Your personal collection of magical tales and creative masterpieces",
      path: "/my-stories",
      color: "from-indigo-500 via-purple-500 to-pink-500",
      glow: "shadow-indigo-500/30",
      requiresAuth: true,
      hoverIcon: Moon
    }
  ];

  const filteredFeatures = features.filter(feature => !feature.requiresAuth || user);

  const confirmationLogic = () => {
    setConfirmDialogOpen(false);
    navigate('/auth/login');
  };

  const stats = [
    { value: "50,000+", label: "Stories Created", icon: Sparkles, description: "Magical tales brought to life" },
    { value: "10,000+", label: "Happy Storytellers", icon: Heart, description: "Creative minds inspired" },
    { value: "25+", label: "Narrator Voices", icon: Users, description: "Professional voice actors" },
    { value: "∞", label: "Possibilities", icon: Zap, description: "Unlimited imagination" }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden perspective-2000">
      {/* 3D Background Scene */}
      {/* <Scene3D /> */}
      
      {/* Particle System */}
      {/* <ParticleSystem /> */}
      
      {/* Family Background Connections */}
      {/* <FamilyBackground /> */}

      {/* Enhanced Interactive Elements */}
      {/* Magic Cursor with Trail */}
      {/* <motion.div
        className="fixed w-8 h-8 bg-gradient-to-r from-purple-500 via-pink-500 to-cyan-500 rounded-full pointer-events-none z-50 mix-blend-difference shadow-lg"
        animate={{
          x: mousePosition.x - 16,
          y: mousePosition.y - 16,
          scale: isInteracting ? 1.5 : 1,
        }}
        transition={{ type: "spring", damping: 20, stiffness: 300 }}
      /> */}

      {/* Click Ripple Effects */}
      {clickRipples.map((ripple) => (
        <motion.div
          key={ripple.id}
          className="fixed pointer-events-none z-40"
          style={{
            left: ripple.x - 25,
            top: ripple.y - 25,
          }}
          initial={{ scale: 0, opacity: 0.8 }}
          animate={{ scale: 3, opacity: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          <div className="w-12 h-12 border-4 border-magic rounded-full bg-magic/20" />
        </motion.div>
      ))}

      {/* Family Interactive Game */}
      {/* <FamilyInteraction /> */}
      
      {/* Floating Interactive Family Elements */}
      

      {/* Dynamic Story Books Floating Around */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={`book-${i}`}
            className="absolute"
            animate={{
              y: [0, -30, 0],
              rotate: [0, 10, -10, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 2
            }}
            style={{
              left: `${10 + i * 25}%`,
              top: `${15 + i * 15}%`,
            }}
          >
            <motion.div 
              className="w-16 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg shadow-2xl shadow-indigo-500/40 flex items-center justify-center cursor-pointer"
              whileHover={{ 
                scale: 1.3,
                rotate: 15,
                boxShadow: "0 20px 40px rgba(99, 102, 241, 0.6)"
              }}
            >
              <BookOpen className="w-8 h-8 text-white" />
            </motion.div>
          </motion.div>
        ))}
      </div>

      {/* Interactive Sparkle System */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`sparkle-${i}`}
            className="absolute w-4 h-4"
            initial={{ 
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              scale: 0
            }}
            animate={{
              scale: [0, 1, 0],
              rotate: [0, 180, 360],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.5,
              ease: "easeInOut"
            }}
          >
            <div className="w-full h-full bg-gradient-to-r from-yellow-300 to-orange-400 rounded-full shadow-lg shadow-yellow-400/60">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Enhanced Animated Background Gradients */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div 
          className="absolute -top-40 -right-40 w-[500px] h-[500px] bg-gradient-radial from-purple-500/30 via-pink-500/20 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            rotate: [0, 120, 240, 360],
            x: [0, 100, -50, 0],
            y: [0, -50, 100, 0],
          }}
          transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
        />
        <motion.div 
          className="absolute -bottom-40 -left-40 w-[600px] h-[600px] bg-gradient-radial from-cyan-500/25 via-blue-500/15 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.4, 1.2],
            rotate: [360, 240, 120, 0],
            x: [0, -100, 50, 0],
            y: [0, 50, -100, 0],
          }}
          transition={{ duration: 35, repeat: Infinity, ease: "linear" }}
        />
        <motion.div 
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-radial from-rose-500/20 via-orange-500/10 to-transparent rounded-full blur-3xl"
          animate={{
            scale: [1, 1.5, 1.2, 1],
            opacity: [0.1, 0.3, 0.2, 0.1],
            rotate: [0, 90, 180, 270, 360],
          }}
          transition={{ duration: 40, repeat: Infinity, ease: "easeInOut" }}
        />
        
        <motion.div 
          className="absolute top-1/4 right-1/4 w-32 h-32 bg-gradient-to-r from-green-400/20 to-emerald-500/20 rounded-full blur-2xl"
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 50, -25, 0],
            y: [0, -30, 15, 0],
          }}
          transition={{ duration: 20, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 space-y-32">
        {/* Enhanced Hero Section */}
        <div className="text-center space-y-12 py-32">
          <MagicalContainer className="space-y-8">
            <div className="relative">
              <ShimmerText>
                <h1 className="text-7xl md:text-9xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500 !leading-relaxed overflow-hidden text-glow ">
                  StoryMagic
                </h1>
              </ShimmerText>
              <motion.div 
                className="absolute -top-8 -right-8 text-6xl"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.2, 1],
                }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              >
                ✨
              </motion.div>
            </div>
            
            <motion.p 
              className="text-2xl md:text-3xl text-muted-foreground max-w-5xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              Where cutting-edge AI meets boundless imagination to create the most
              <span className="text-magic font-bold"> extraordinary stories </span>
              the world has ever seen
            </motion.p>
          </MagicalContainer>
          
          {user ? (
            <MagicalContainer delay={0.7} className="flex flex-col sm:flex-row gap-8 justify-center items-center">
              <Link to="/generate">
                <MorphingButton className="btn-magic text-xl px-12 py-8 rounded-2xl group font-bold">
                  <PulsingIcon>
                    <Wand2 className="w-7 h-7 mr-3" />
                  </PulsingIcon>
                  Create New Story
                  <ArrowRight className="w-6 h-6 ml-3 group-hover:translate-x-2 transition-transform" />
                </MorphingButton>
              </Link>
              <Link to="/my-stories">
                <Button size="lg" variant="default" className="text-xl px-12 py-8 rounded-2xl group border-2 border-white/30">
                  <BookOpen className="w-7 h-7 mr-3 group-hover:scale-110 transition-transform" />
                  My Stories
                </Button>
              </Link>
            </MagicalContainer>
          ) : (
            <MagicalContainer delay={0.7} className="space-y-8">
              <div onClick={() => setConfirmDialogOpen(true)}>
                <MorphingButton className="bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 text-white text-2xl px-16 py-10 rounded-3xl group font-black shadow-2xl hover:shadow-purple-500/50">
                  <PulsingIcon>
                    <Sparkles className="w-8 h-8 mr-4" />
                  </PulsingIcon>
                  Try Story Generation
                  <Crown className="w-7 h-7 ml-4" />
                </MorphingButton>
              </div>
              <motion.p 
                className="text-lg text-muted-foreground"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                No account needed to try! 
                <span className="text-magic font-bold"> Sign up to save your magical creations.</span>
              </motion.p>
            </MagicalContainer>
          )}
        </div>

        {/* Enhanced Features Grid */}
        <div className="space-y-16">
          <MagicalContainer className="text-center space-y-6">
            <h2 className="text-5xl md:text-6xl font-black text-magic">Magical Features</h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto">
              Discover the endless possibilities of AI-powered storytelling with our revolutionary tools
            </p>
          </MagicalContainer>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {filteredFeatures.map((feature, index) => (
              <FloatingCard 
                key={index} 
                delay={index * 0.1}
                className="group"
              >
                <Link 
                  to={feature.path} 
                  onClick={() => {if(feature.path === "/") setConfirmDialogOpen(true)}}
                  className="block h-full"
                >
                  <Card className="feature-card h-full cursor-pointer border-0 rounded-3xl p-2 morph-card">
                    <CardHeader className="text-center space-y-8 p-10">
                      <div className="relative">
                        <motion.div 
                          className={`w-24 h-24 mx-auto rounded-3xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-2xl ${feature.glow} icon-3d`}
                          whileHover={{ rotateY: 180 }}
                          transition={{ duration: 0.6 }}
                        >
                          <motion.div
                            initial={{ rotateY: 0 }}
                            whileHover={{ rotateY: 180 }}
                            transition={{ duration: 0.6 }}
                            className="relative w-full h-full flex items-center justify-center"
                          >
                            <feature.icon className="w-12 h-12 text-white drop-shadow-lg absolute backface-hidden" />
                            {feature.hoverIcon && (
                              <feature.hoverIcon className="w-12 h-12 text-white drop-shadow-lg absolute backface-hidden rotate-y-180" />
                            )}
                          </motion.div>
                        </motion.div>
                        <motion.div
                          className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-xl"
                          animate={{ scale: [1, 1.1, 1], opacity: [0.3, 0.6, 0.3] }}
                          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                        />
                      </div>
                      
                      <div className="space-y-4">
                        <CardTitle className="text-2xl font-bold group-hover:text-magic transition-all duration-300">
                          {feature.title}
                        </CardTitle>
                        <CardDescription className="text-lg text-center leading-relaxed">
                          {feature.description}
                        </CardDescription>
                      </div>
                    </CardHeader>
                  </Card>
                </Link>
              </FloatingCard>
            ))}
          </div>
        </div>

        {/* Enhanced Featured Stories */}
        <div className="space-y-16">
          <div className="flex items-center justify-between">
            <MagicalContainer className="space-y-4">
              <h2 className="text-5xl md:text-6xl font-black text-magic">Featured Stories</h2>
              <p className="text-2xl text-muted-foreground">Hand-picked tales to spark your imagination</p>
            </MagicalContainer>
            <Link to="/free-stories">
              <Button variant="outline" size="lg" className="rounded-2xl group border-2 border-white/30 px-8 py-4">
                View All Stories
                <Heart className="w-6 h-6 ml-3 group-hover:scale-110 transition-transform text-pink-400" />
              </Button>
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {freeStories.map((story, index) => (
              <FloatingCard 
                key={story.id} 
                delay={index * 0.15}
                className="group"
              >
                <Card className="story-card border-0 rounded-3xl overflow-hidden group cursor-pointer h-full">
                  <div className="aspect-[16/10] relative overflow-hidden">
                    {story.thumbnail ? (
                      <motion.img 
                        src={story.thumbnail} 
                        alt={story.title}
                        className="w-full h-full object-cover"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.7 }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-purple-400 via-pink-400 to-blue-400"></div>
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                    <motion.div 
                      className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100"
                      transition={{ duration: 0.3 }}
                    >
                      <motion.div 
                        className="w-20 h-20 rounded-full glass-card flex items-center justify-center"
                        whileHover={{ scale: 1.1, rotate: 360 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Play className="w-10 h-10 text-white ml-1" />
                      </motion.div>
                    </motion.div>
                    <Badge 
                      variant="secondary" 
                      className="absolute top-6 right-6 glass-card border-white/30 text-white px-4 py-2"
                    >
                      <Clock className="w-4 h-4 mr-2" />
                      {story.duration}m
                    </Badge>
                  </div>
                  
                  <CardHeader className="space-y-6 p-8">
                    <CardTitle className="text-2xl leading-tight group-hover:text-magic transition-colors">
                      {story.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-3 text-lg leading-relaxed">
                      {story.content.substring(0, 120)}...
                    </CardDescription>
                  </CardHeader>
                  
                  <CardContent className="space-y-6 p-8 pt-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                        <span className="text-muted-foreground font-medium">
                          {story.narrator}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Star className="w-5 h-5 text-yellow-500 fill-current" />
                        <span className="font-bold text-lg">{story.likes.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {story.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="outline" className="border-white/20 text-sm px-3 py-1">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </FloatingCard>
            ))}
          </div>
        </div>

        {/* Revolutionary Stats Section */}
        <div className="relative">
          <FloatingCard delay={0.3}>
            <div className="glass-card rounded-3xl p-16 relative overflow-hidden morph-card">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-blue-500/10"></div>
              <div className="relative z-10 space-y-16">
                <div className="text-center space-y-6">
                  <h2 className="text-5xl md:text-6xl font-black text-magic">Join the Revolution</h2>
                  <p className="text-2xl text-muted-foreground max-w-4xl mx-auto">
                    Be part of the fastest-growing AI storytelling community transforming creativity
                  </p>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-12">
                  {stats.map((stat, index) => (
                    <MagicalContainer 
                      key={index} 
                      delay={0.1 * index}
                      className="text-center space-y-6"
                    >
                      <PulsingIcon className="w-20 h-20 mx-auto rounded-3xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                        <stat.icon className="w-10 h-10 text-white" />
                      </PulsingIcon>
                      <div className="space-y-2">
                        <div className="text-5xl md:text-6xl font-black text-magic">{stat.value}</div>
                        <div className="text-xl font-bold text-foreground">{stat.label}</div>
                        <div className="text-muted-foreground">{stat.description}</div>
                      </div>
                    </MagicalContainer>
                  ))}
                </div>
                
                <div className="text-center">
                  <MorphingButton 
                    className="btn-magic rounded-2xl px-12 py-6 text-xl font-bold group"
                    onClick={() => setConfirmDialogOpen(true)}
                  >
                    <Rocket className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform" />
                    Start Your Journey
                    <Sparkles className="w-6 h-6 ml-3 group-hover:scale-110 transition-transform" />
                  </MorphingButton>
                </div>
              </div>
            </div>
          </FloatingCard>
        </div>
      </div>

      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={confirmationLogic}
        title="✨ Welcome to StoryMagic"
        description={
          <div className="space-y-3">
            <p className="text-lg">You need to be logged in to create magical stories with our AI.</p>
            <p className="text-magic font-bold">Ready to begin your storytelling adventure?</p>
          </div>
        }
        confirmText="Login Now"
        cancelText="Maybe Later"
        variant="default"
      />
    </div>
  );
};

export default Index;