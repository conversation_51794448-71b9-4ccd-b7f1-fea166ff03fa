import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface MagicalContainerProps {
  children: ReactNode;
  className?: string;
  delay?: number;
}

export function MagicalContainer({ children, className = '', delay = 0 }: MagicalContainerProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.8, 
        delay,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        scale: 1.02,
        transition: { duration: 0.3 }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function FloatingCard({ children, className = '', delay = 0 }: MagicalContainerProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 100, rotateX: -15 }}
      animate={{ opacity: 1, y: 0, rotateX: 0 }}
      transition={{ 
        duration: 1, 
        delay,
        type: "spring",
        stiffness: 80
      }}
      whileHover={{ 
        y: -20,
        rotateX: 5,
        transition: { duration: 0.3 }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function PulsingIcon({ children, className = '' }: { children: ReactNode; className?: string }) {
  return (
    <motion.div
      animate={{ 
        scale: [1, 1.1, 1],
        rotate: [0, 5, -5, 0]
      }}
      transition={{ 
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }}
      whileHover={{
        scale: 1.2,
        rotate: 360,
        transition: { duration: 0.5 }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function ShimmerText({ children, className = '' }: { children: ReactNode; className?: string }) {
  return (
    <motion.div
      className={`relative overflow-hidden ${className}`}
      whileHover="hover"
    >
      <motion.div
        variants={{
          hover: {
            backgroundPosition: '200% center',
          }
        }}
        animate={{
          backgroundPosition: ['0% center', '200% center', '0% center'],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "linear"
        }}
        className="bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:200%_100%] absolute inset-0"
      />
      {children}
    </motion.div>
  );
}

export function MorphingButton({ children, className = '', ...props }: any) {
  return (
    <motion.button
      whileHover={{ 
        scale: 1.05,
        boxShadow: "0 20px 40px rgba(156, 39, 176, 0.3)"
      }}
      whileTap={{ scale: 0.95 }}
      animate={{
        boxShadow: [
          "0 0 20px rgba(156, 39, 176, 0.2)",
          "0 0 40px rgba(255, 105, 180, 0.3)",
          "0 0 20px rgba(156, 39, 176, 0.2)"
        ]
      }}
      transition={{ 
        boxShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" },
        scale: { duration: 0.2 }
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.button>
  );
}