﻿#  StoryMagic - AI-Powered Children's Storytelling App

An interactive storytelling application that uses AI to generate magical stories for children, complete with voice narration and customizable characters.

##  Features

###  MVP 1: Basic AI Story Generation + Voice Output 
- **AI-Powered Story Generation**: OpenAI GPT-4 integration for creating engaging children's stories
- **Text-to-Speech**: ElevenLabs API integration with Web Speech API fallback
- **Story Customization**: Choose story length, genre, and age group
- **Professional Audio Player**: Full controls with progress bar, volume, and time display
- **Fallback System**: Works even without API keys using template-based generation

##  Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Install dependencies**:
   `ash
   npm install
   `

2. **Set up environment variables**:
   `ash
   cp .env.example .env
   `

3. **Add your API keys to .env** (optional for demo mode):
   `env
   # For AI-powered story generation
   VITE_OPENAI_API_KEY=your_openai_api_key_here
   
   # For premium voice narration (optional)
   VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
   `

4. **Start the development server**:
   `ash
   npm run dev
   `

5. **Open your browser** to http://localhost:5173

##  API Setup (Optional)

### OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account and get your API key
3. Add it to your .env file as VITE_OPENAI_API_KEY

### ElevenLabs API Key (Optional)
1. Go to [ElevenLabs](https://elevenlabs.io/)
2. Create an account and get your API key
3. Add it to your .env file as VITE_ELEVENLABS_API_KEY

**Note**: The app works in demo mode without API keys, using template-based story generation and Web Speech API for narration.

##  Usage

1. **Create Stories**: Enter a prompt and customize length, genre, and age group
2. **Choose Narrator**: Select from available voice narrators
3. **Generate Audio**: Stories are automatically converted to speech
4. **Save & Share**: Save stories to your collection (requires login)

##  Troubleshooting

### Common Issues

**"OpenAI API Key Missing" Error**:
- The app will work in demo mode without an API key
- Add your OpenAI API key to .env for AI-powered stories

**Audio Not Playing**:
- Check browser permissions for audio
- Try refreshing the page
- Ensure Web Speech API is supported in your browser

##  Next Steps

Your MVP 1 is complete! Consider:
- Adding your OpenAI API key for real AI stories
- Testing different story prompts and configurations
- Moving on to MVP 2 implementation
