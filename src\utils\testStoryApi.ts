// Test file for story API integration
import { generateStoryWithPayload, createStoryPayload } from './storyApi';

export const testStoryGeneration = async () => {
  console.log('Testing story generation with external API...');

  try {
    const payload = createStoryPayload(
      "A magical adventure in an enchanted forest",
      [
        {
          name: "<PERSON>",
          traits: ["brave", "curious", "magical"]
        }
      ],
      "8-12",
      "short",
      "fantasy",
      true
    );

    console.log('Payload being sent:', JSON.stringify(payload, null, 2));

    const result = await generateStoryWithPayload(payload);

    console.log('Story generation result:', {
      id: result.id,
      title: result.title,
      wordCount: result.word_count,
      estimatedReadingTime: result.estimated_reading_time,
      ageGroup: result.age_group,
      charactersUsed: result.characters_used,
      hasAudio: !!result.audio_base64,
      audioLength: result.audio_base64?.length || 0,
      storyPreview: result.story.substring(0, 200) + '...'
    });

    return result;
  } catch (error) {
    console.error('Test failed:', error);
    throw error;
  }
};

// Test with empty characters (like the issue you mentioned)
export const testEmptyCharacters = async () => {
  console.log('Testing story generation with empty characters...');

  try {
    const payload = createStoryPayload(
      "A group of toys come to life when no one is watching",
      [], // Empty characters array
      "8-12",
      "medium",
      "Adventure",
      true
    );

    console.log('Empty characters payload:', JSON.stringify(payload, null, 2));

    const result = await generateStoryWithPayload(payload);

    console.log('Result with empty characters:', {
      id: result.id,
      title: result.title,
      charactersUsed: result.characters_used,
      storyPreview: result.story.substring(0, 200) + '...'
    });

    return result;
  } catch (error) {
    console.error('Empty characters test failed:', error);
    throw error;
  }
};

// Test the complete story response structure
export const testStoryResponseStructure = async () => {
  console.log('Testing complete story response structure...');

  try {
    const payload = createStoryPayload(
      "A group of toys come to life when no one is watching",
      [
        { name: "Buzz", traits: ["brave", "loyal", "space-ranger"] },
        { name: "Woody", traits: ["friendly", "leader", "cowboy"] }
      ],
      "8-12",
      "medium",
      "Adventure",
      true
    );

    console.log('Testing with payload:', JSON.stringify(payload, null, 2));

    const result = await generateStoryWithPayload(payload);

    console.log('Complete API Response Structure:');
    console.log('================================');
    console.log('ID:', result.id);
    console.log('Title:', result.title);
    console.log('Word Count:', result.word_count);
    console.log('Reading Time:', result.estimated_reading_time, 'minutes');
    console.log('Age Group:', result.age_group);
    console.log('Characters Used:', result.characters_used);
    console.log('Created At:', result.created_at);
    console.log('Updated At:', result.updated_at);
    console.log('Is Regenerated:', result.is_regenerated);
    console.log('Has Audio:', !!result.audio_base64);
    console.log('Audio Length:', result.audio_base64?.length || 0, 'characters');
    console.log('Story Preview:', result.story.substring(0, 150) + '...');
    console.log('================================');

    return result;
  } catch (error) {
    console.error('Complete response test failed:', error);
    throw error;
  }
};

// Make the test functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testStoryGeneration = testStoryGeneration;
  (window as any).testEmptyCharacters = testEmptyCharacters;
  (window as any).testStoryResponseStructure = testStoryResponseStructure;
}

// You can call these functions from the browser console to test:
// testStoryGeneration().then(result => console.log('Test completed:', result))
// testEmptyCharacters().then(result => console.log('Empty characters test completed:', result))
// testStoryResponseStructure().then(result => console.log('Complete response test completed:', result))
