
import { SidebarTrigger } from "@/components/ui/sidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { LoginDialog } from "@/components/LoginDialog";
import { useState } from "react";
import { LogOut, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";

export function Header() {
  const { user, logout } = useAuth();
  const [showLogin, setShowLogin] = useState(false);

  return (
    <header className="h-16 border-b bg-background/80 backdrop-blur-sm flex items-center justify-between px-4">
      <div className="flex items-center gap-4">
        <SidebarTrigger />
        <div className="text-sm text-muted-foreground">
          Welcome to your magical storytelling adventure! ✨
        </div>
      </div>

      <div className="flex items-center gap-4">
        {user ? (
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="text-xs">
                  {user.avatar || user.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="hidden sm:block">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{user.name}</span>
                  <Badge variant={user.accountType === 'child' ? 'secondary' : 'default'} className="text-xs">
                    {user.accountType === 'child' ? '👶 Kid' : '👨‍👩‍👧‍👦 Parent'}
                  </Badge>
                </div>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={logout}
              className="text-muted-foreground hover:text-foreground"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        ) : (
          <Button 
            onClick={() => setShowLogin(true)}
            className="story-gradient text-white hover:opacity-90"
          >
            Login / Sign Up
          </Button>
        )}
      </div>

      <LoginDialog open={showLogin} onOpenChange={setShowLogin} />
    </header>
  );
}
